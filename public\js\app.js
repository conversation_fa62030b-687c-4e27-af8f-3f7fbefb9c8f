// 全局变量
let currentUnit = 'school';
let currentUser = null;

// 页面初始化
document.addEventListener('DOMContentLoaded', async function() {
    // 初始化数据
    await dataManager.init();
    
    // 显示当前访问地址
    try {
        const currentUrl = window.location;
        let displayAddress = `${currentUrl.hostname}:${currentUrl.port || '3000'}`;

        // 如果是localhost，尝试获取局域网地址供参考
        if (currentUrl.hostname === 'localhost' || currentUrl.hostname === '127.0.0.1') {
            try {
                const serverInfo = await api.getServerInfo();
                if (serverInfo.networkIps && serverInfo.networkIps.length > 0) {
                    const lanIp = serverInfo.networkIps[0];
                    displayAddress = `${displayAddress} (局域网: ${lanIp}:${currentUrl.port || '3000'})`;
                }
            } catch (error) {
                // 如果获取失败，只显示当前地址
                console.log('获取局域网地址失败:', error);
            }
        }

        document.getElementById('currentIp').textContent = displayAddress;
    } catch (error) {
        console.error('获取当前地址失败:', error);
        document.getElementById('currentIp').textContent = '地址获取失败';
    }
    
    // 显示登录模态框
    showModal('loginModal');
});

// 模态框管理类
class ModalManager {
    constructor() {
        this.activeModals = new Set();
        this.setupGlobalListeners();
    }

    // 设置全局监听器
    setupGlobalListeners() {
        // ESC键关闭模态框
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.activeModals.size > 0) {
                const lastModal = Array.from(this.activeModals).pop();
                this.close(lastModal);
            }
        });

        // 点击背景关闭模态框
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal') && e.target.classList.contains('active')) {
                const modalId = e.target.id;
                this.close(modalId);
            }
        });
    }

    // 显示模态框
    show(modalId) {
        const modal = document.getElementById(modalId);
        if (!modal) {
            console.error(`Modal with id "${modalId}" not found`);
            return;
        }

        // 添加到活动模态框集合
        this.activeModals.add(modalId);

        // 显示模态框
        modal.classList.add('active');

        // 设置焦点到模态框内的第一个可聚焦元素
        setTimeout(() => {
            const firstFocusable = modal.querySelector('input, button, select, textarea, [tabindex]:not([tabindex="-1"])');
            if (firstFocusable) {
                firstFocusable.focus();
            }
        }, 100);

        // 防止背景滚动
        document.body.style.overflow = 'hidden';
    }

    // 关闭模态框
    close(modalId) {
        const modal = document.getElementById(modalId);
        if (!modal) {
            console.error(`Modal with id "${modalId}" not found`);
            return;
        }

        // 从活动模态框集合中移除
        this.activeModals.delete(modalId);

        // 隐藏模态框
        modal.classList.remove('active');

        // 如果没有其他活动模态框，恢复背景滚动
        if (this.activeModals.size === 0) {
            document.body.style.overflow = '';
        }

        // 清理表单数据（如果需要）
        this.clearModalForm(modal);
    }

    // 清理模态框表单
    clearModalForm(modal) {
        const forms = modal.querySelectorAll('form');
        forms.forEach(form => {
            // 只清理非登录表单
            if (!form.closest('#loginModal')) {
                form.reset();
            }
        });
    }

    // 检查是否有活动模态框
    hasActiveModals() {
        return this.activeModals.size > 0;
    }

    // 获取当前活动的模态框
    getActiveModals() {
        return Array.from(this.activeModals);
    }
}

// 创建模态框管理器实例
const modalManager = new ModalManager();

// 兼容性函数（保持原有API）
function showModal(modalId) {
    modalManager.show(modalId);
}

function closeModal(modalId) {
    modalManager.close(modalId);
}

// 登录功能
async function login() {
    const username = document.getElementById('loginUsername').value;
    const password = document.getElementById('loginPassword').value;
    const level = document.getElementById('loginType').value;
    
    if (!username || !password) {
        utils.showMessage('请输入用户名和密码', 'error');
        return;
    }
    
    try {
        const result = await api.login(username, password, level);
        
        if (result.success) {
            currentUser = result.user;
            dataManager.setCurrentUser(currentUser);
            
            // 更新界面显示
            updateUserInterface();
            
            // 关闭登录模态框
            closeModal('loginModal');
            
            // 初始化主界面
            initMainInterface();
            
            utils.showMessage('登录成功', 'success');
        } else {
            utils.showMessage(result.message, 'error');
        }
    } catch (error) {
        console.error('登录失败:', error);
        utils.showMessage('登录失败，请检查网络连接', 'error');
    }
}

// 退出登录
function logout() {
    if (utils.confirm('确定要退出登录吗？')) {
        currentUser = null;
        dataManager.setCurrentUser(null);
        currentUnit = 'school';
        
        // 清空界面
        document.getElementById('unitTree').innerHTML = '';
        document.getElementById('student-table-body').innerHTML = '';
        chartManager.destroyAllCharts();
        
        // 显示登录模态框
        showModal('loginModal');
        
        // 清空登录表单
        document.getElementById('loginUsername').value = '';
        document.getElementById('loginPassword').value = '';
    }
}

// 更新用户界面
function updateUserInterface() {
    const userLevelMap = {
        'school': { text: '学校级管理员', class: 'school-level' },
        'college': { text: '学院级管理员', class: 'college-level' },
        'class': { text: '班级管理员', class: 'class-level' }
    };
    
    const levelInfo = userLevelMap[currentUser.level] || { text: '未知', class: '' };
    
    document.getElementById('currentUserName').textContent = currentUser.username;
    document.getElementById('currentUser').textContent = currentUser.username;
    
    const userLevelElement = document.getElementById('userLevel');
    userLevelElement.textContent = levelInfo.text;
    userLevelElement.className = `user-level ${levelInfo.class}`;
    
    // 根据用户权限显示/隐藏管理员面板
    const adminPanel = document.getElementById('adminPanel');
    if (currentUser.level === 'school') {
        adminPanel.style.display = 'block';
    } else {
        adminPanel.style.display = 'none';
    }
}

// 初始化主界面
function initMainInterface() {
    // 设置当前单位
    const units = dataManager.getUnits();
    const unit = units.find(u => u.id === currentUser.unit);
    if (unit) {
        document.getElementById('current-unit').textContent = unit.name;
        currentUnit = currentUser.unit;
    }

    // 设置单位树选中状态
    unitTreeManager.setSelectedNode(currentUnit);

    // 渲染界面组件
    renderUnitTree();
    renderStudentTable();
    updateStats();
    initCharts(currentUnit);
    loadUnitOptions();

    // 设置表单验证
    setupStudentFormValidation();

    // 设置其他事件监听器
    setupEventListeners();
}

// 设置事件监听器
function setupEventListeners() {
    // 表单提交事件
    const studentForm = document.getElementById('studentForm');
    if (studentForm) {
        studentForm.addEventListener('submit', (e) => {
            e.preventDefault();
            saveStudent();
        });
    }

    // 文件选择事件
    const excelFile = document.getElementById('excelFile');
    if (excelFile) {
        excelFile.addEventListener('change', (e) => {
            const file = e.target.files[0];
            if (file) {
                const fileName = file.name;
                const uploadBox = document.querySelector('.upload-box p');
                if (uploadBox) {
                    uploadBox.textContent = `已选择: ${fileName}`;
                }
            }
        });
    }

    // 全选复选框事件
    const selectAllCheckbox = document.getElementById('selectAll');
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', toggleSelectAll);
    }

    // 状态变化事件
    const statusSelect = document.getElementById('studentStatus');
    if (statusSelect) {
        statusSelect.addEventListener('change', toggleAbsentFields);
    }
}

// 单位树管理类
class UnitTreeManager {
    constructor() {
        this.expandedNodes = new Set(['school']); // 默认展开学校节点
        this.selectedNode = 'school';
    }

    // 初始化展开状态
    initializeExpandedState() {
        if (!currentUser) return;

        const units = dataManager.getUnits();
        const userUnit = units.find(u => u.id === currentUser.unit);

        if (userUnit) {
            if (userUnit.type === 'class') {
                // 如果用户是班级管理员，展开其所属学院
                this.expandedNodes.add(userUnit.parent);
            } else if (userUnit.type === 'college') {
                // 如果用户是学院管理员，展开该学院
                this.expandedNodes.add(userUnit.id);
            }
        }

        // 如果是学校管理员，展开所有有班级的学院
        if (currentUser.level === 'school') {
            const colleges = units.filter(u => u.type === 'college');
            colleges.forEach(college => {
                // 检查学院下是否有班级
                const hasClasses = units.some(u => u.parent === college.id && u.type === 'class');
                if (hasClasses) {
                    this.expandedNodes.add(college.id);
                }
            });
        }
    }

    // 渲染单位树
    render() {
        // 初始化展开状态
        this.initializeExpandedState();

        const units = dataManager.getUnits();
        const school = units.find(u => u.type === 'school');
        const colleges = units.filter(u => u.type === 'college');

        let html = this.renderSchoolNode(school);

        colleges.forEach(college => {
            if (!dataManager.hasPermission(college.id)) return;
            html += this.renderCollegeNode(college, units);
        });

        document.getElementById('unitTree').innerHTML = html;
        this.attachEventListeners();
    }

    // 渲染学校节点
    renderSchoolNode(school) {
        const isSelected = this.selectedNode === 'school';
        const hasEditPermission = currentUser && currentUser.level === 'school';

        return `
            <li class="tree-node ${isSelected ? 'selected' : ''}" data-unit-id="school" data-unit-type="school">
                <div class="tree-node-content">
                    <span class="tree-node-label" onclick="unitTreeManager.selectNode('school', event)">
                        <i class="fas fa-school tree-icon"></i>
                        <span class="tree-text">${school.name}</span>
                    </span>
                    ${hasEditPermission ? `
                    <div class="tree-actions">
                        <button class="tree-action-btn" onclick="unitTreeManager.editNode('school', event)" title="编辑">
                            <i class="fas fa-edit"></i>
                        </button>
                    </div>` : ''}
                </div>
            </li>
        `;
    }

    // 渲染学院节点
    renderCollegeNode(college, units) {
        const classes = units.filter(u => u.parent === college.id);
        const isSelected = this.selectedNode === college.id;
        const isExpanded = this.expandedNodes.has(college.id);
        const hasEditPermission = dataManager.hasPermission(college.id);
        const hasDeletePermission = currentUser && currentUser.level === 'school';

        // 过滤有权限查看的班级
        const visibleClasses = classes.filter(cls => {
            // 学校管理员可以看到所有班级
            if (currentUser && currentUser.level === 'school') {
                return true;
            }
            // 其他用户按权限过滤
            return dataManager.hasPermission(cls.id);
        });

        // 如果没有可见的班级，不显示展开图标
        const hasChildren = visibleClasses.length > 0;

        let html = `
            <li class="tree-node ${isSelected ? 'selected' : ''}" data-unit-id="${college.id}" data-unit-type="college">
                <div class="tree-node-content">
                    <span class="tree-node-label">
                        ${hasChildren ? `
                        <i class="fas ${isExpanded ? 'fa-chevron-down' : 'fa-chevron-right'} tree-expand-icon"
                           onclick="event.stopPropagation(); unitTreeManager.toggleNode('${college.id}', event)"
                           title="${isExpanded ? '折叠' : '展开'}"></i>
                        ` : `
                        <i class="tree-expand-placeholder"></i>
                        `}
                        <i class="fas fa-building tree-icon"></i>
                        <span class="tree-text" onclick="unitTreeManager.selectNode('${college.id}', event)">${college.name}</span>
                    </span>
                    ${hasEditPermission ? `
                    <div class="tree-actions">
                        <button class="tree-action-btn" onclick="unitTreeManager.editNode('${college.id}', event)" title="编辑">
                            <i class="fas fa-edit"></i>
                        </button>
                        ${hasDeletePermission ? `
                        <button class="tree-action-btn delete-btn" onclick="unitTreeManager.deleteNode('${college.id}', event)" title="删除">
                            <i class="fas fa-trash"></i>
                        </button>` : ''}
                    </div>` : ''}
                </div>
                ${hasChildren ? `
                <ul class="tree-children ${isExpanded ? 'expanded' : 'collapsed'}">
                    ${visibleClasses.map(cls => this.renderClassNode(cls)).join('')}
                </ul>` : ''}
            </li>
        `;

        return html;
    }

    // 渲染班级节点
    renderClassNode(cls) {
        const isSelected = this.selectedNode === cls.id;
        const hasEditPermission = dataManager.hasPermission(cls.id);
        const hasDeletePermission = currentUser && currentUser.level === 'school';

        return `
            <li class="tree-node ${isSelected ? 'selected' : ''}" data-unit-id="${cls.id}" data-unit-type="class">
                <div class="tree-node-content">
                    <span class="tree-node-label" onclick="unitTreeManager.selectNode('${cls.id}', event)">
                        <i class="fas fa-users tree-icon"></i>
                        <span class="tree-text">${cls.name}</span>
                    </span>
                    ${hasEditPermission ? `
                    <div class="tree-actions">
                        <button class="tree-action-btn" onclick="unitTreeManager.editNode('${cls.id}', event)" title="编辑">
                            <i class="fas fa-edit"></i>
                        </button>
                        ${hasDeletePermission ? `
                        <button class="tree-action-btn delete-btn" onclick="unitTreeManager.deleteNode('${cls.id}', event)" title="删除">
                            <i class="fas fa-trash"></i>
                        </button>` : ''}
                    </div>` : ''}
                </div>
            </li>
        `;
    }

    // 附加事件监听器
    attachEventListeners() {
        // 为树节点添加键盘导航支持
        document.getElementById('unitTree').addEventListener('keydown', (e) => {
            if (e.key === 'ArrowUp' || e.key === 'ArrowDown') {
                e.preventDefault();
                this.navigateWithKeyboard(e.key === 'ArrowDown');
            } else if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                const selectedElement = document.querySelector('.tree-node.selected');
                if (selectedElement) {
                    const unitId = selectedElement.dataset.unitId;
                    this.selectNode(unitId);
                }
            }
        });
    }

    // 选择节点
    selectNode(unitId, event) {
        if (event) {
            event.stopPropagation();
        }

        this.selectedNode = unitId;
        currentUnit = unitId;

        // 更新UI选中状态
        document.querySelectorAll('.tree-node').forEach(node => {
            node.classList.remove('selected');
        });

        const selectedElement = document.querySelector(`[data-unit-id="${unitId}"]`);
        if (selectedElement) {
            selectedElement.classList.add('selected');
        }

        // 更新当前单位显示
        const units = dataManager.getUnits();
        const unit = units.find(u => u.id === unitId);
        if (unit) {
            document.getElementById('current-unit').textContent = unit.name;
        }

        // 更新表格和图表
        renderStudentTable();
        updateStats();
        initCharts(currentUnit);
    }

    // 切换节点展开/折叠
    toggleNode(unitId, event) {
        if (event) {
            event.stopPropagation();
        }

        if (this.expandedNodes.has(unitId)) {
            this.expandedNodes.delete(unitId);
        } else {
            this.expandedNodes.add(unitId);
        }

        this.render(); // 重新渲染以更新展开状态
    }

    // 编辑节点
    editNode(unitId, event) {
        if (event) {
            event.stopPropagation();
        }
        editUnit(unitId, event);
    }

    // 删除节点
    deleteNode(unitId, event) {
        if (event) {
            event.stopPropagation();
        }
        deleteUnit(unitId, event);
    }

    // 键盘导航
    navigateWithKeyboard(down) {
        const nodes = Array.from(document.querySelectorAll('.tree-node'));
        const currentIndex = nodes.findIndex(node => node.classList.contains('selected'));

        let nextIndex;
        if (down) {
            nextIndex = currentIndex < nodes.length - 1 ? currentIndex + 1 : 0;
        } else {
            nextIndex = currentIndex > 0 ? currentIndex - 1 : nodes.length - 1;
        }

        const nextNode = nodes[nextIndex];
        if (nextNode) {
            const unitId = nextNode.dataset.unitId;
            this.selectNode(unitId);
        }
    }

    // 展开到指定节点
    expandToNode(unitId) {
        const units = dataManager.getUnits();
        const unit = units.find(u => u.id === unitId);

        if (unit && unit.type === 'class') {
            // 如果是班级，展开其父学院
            this.expandedNodes.add(unit.parent);
        }

        this.render();
    }

    // 获取当前选中节点
    getSelectedNode() {
        return this.selectedNode;
    }

    // 设置选中节点
    setSelectedNode(unitId) {
        this.selectedNode = unitId;
        this.expandToNode(unitId);
    }
}

// 创建单位树管理器实例
const unitTreeManager = new UnitTreeManager();

// 兼容性函数（保持原有API）
function renderUnitTree() {
    unitTreeManager.render();
}

function selectUnit(unitId, event) {
    unitTreeManager.selectNode(unitId, event);
}

// 渲染学生表格
function renderStudentTable() {
    const tbody = document.getElementById('student-table-body');
    tbody.innerHTML = '';

    const statusFilter = document.getElementById('statusFilter').value;
    const typeFilter = document.getElementById('typeFilter').value;

    let students = chartManager.getStudentsForUnit(currentUnit);

    // 应用筛选条件
    if (statusFilter) {
        students = students.filter(s => s.status === statusFilter);
    }
    if (typeFilter) {
        students = students.filter(s => s.type === typeFilter);
    }

    // 按单位树排列学生（学校账号查看全体时）
    if (currentUnit === 'school' && currentUser && currentUser.level === 'school') {
        students = sortStudentsByUnitTree(students);
    }

    const units = dataManager.getUnits();
    
    students.forEach((student, index) => {
        const unit = units.find(u => u.id === student.unit);
        const unitName = unit ? unit.name : '未知单位';
        
        // 状态显示
        let statusText = student.status === 'present' ? '在位' : '不在位';
        let statusClass = student.status === 'present' ? 'status-present' : 'status-absent';
        
        // 位置类型显示和统计逻辑
        let locationTypeText = '';
        let effectiveStatus = student.status; // 用于统计的实际状态

        if (student.status === 'absent') {
            if (student.locationType === 'internal') {
                locationTypeText = '(单位内)';
                statusClass = 'status-internal';
                // 单位内不在位：对班级/学院算不在位，对学校算在位
                if (currentUser && currentUser.level === 'school') {
                    effectiveStatus = 'present'; // 学校级别算在位
                    statusText = '在位'; // 显示文本也要相应调整
                }
            } else {
                locationTypeText = '(单位外)';
                effectiveStatus = 'absent'; // 单位外统一算不在位
            }
        }
        
        // 原因显示
        let reasonText = '';
        let reasonClass = '';
        if (student.status === 'absent' && student.reason) {
            const reasonMap = {
                'vacation': '休假',
                'training': '培训',
                'secondment': '借调',
                'business': '出差'
            };
            reasonText = reasonMap[student.reason] || student.reason;
            reasonClass = `reason-${student.reason}`;
        }
        
        const row = tbody.insertRow();
        row.innerHTML = `
            <td>
                <input type="checkbox" class="student-checkbox" value="${student.id}">
                ${index + 1}
            </td>
            <td>${student.name}</td>
            <td>${student.id}</td>
            <td>${student.idNumber}</td>
            <td>${student.workDate}</td>
            <td>${student.type}</td>
            <td>${student.major}</td>
            <td>${unitName}</td>
            <td class="${statusClass}">${statusText} ${locationTypeText}</td>
            <td class="${reasonClass}">${reasonText}</td>
            <td>${student.location || '-'}</td>
            <td>${student.contacts.self}<br>${student.contacts.parent}(父/母)</td>
            <td>
                <button class="action-btn edit-btn" onclick="openEditModal('${student.id}')">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="action-btn delete-btn" onclick="deleteStudent('${student.id}', event)">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        `;
    });
}

// 更新统计信息
function updateStats() {
    const students = chartManager.getStudentsForUnit(currentUnit);
    const totalCount = students.length;

    // 使用与图表相同的统计逻辑
    const { presentCount, absentCount } = chartManager.calculateEffectiveStatus(students);

    document.getElementById('totalCount').textContent = totalCount;
    document.getElementById('presentCount').textContent = presentCount;
    document.getElementById('absentCount').textContent = absentCount;
}

// 刷新数据
async function refreshData() {
    try {
        await dataManager.refresh();
        utils.showMessage('数据刷新成功', 'success');
    } catch (error) {
        console.error('数据刷新失败:', error);
        utils.showMessage('数据刷新失败', 'error');
    }
}

// 按单位树排列学生
function sortStudentsByUnitTree(students) {
    const units = dataManager.getUnits();
    const colleges = units.filter(u => u.type === 'college').sort((a, b) => a.name.localeCompare(b.name));

    const sortedStudents = [];

    colleges.forEach(college => {
        // 获取学院下的班级，按名称排序
        const classes = units.filter(u => u.parent === college.id && u.type === 'class')
                             .sort((a, b) => a.name.localeCompare(b.name));

        classes.forEach(cls => {
            // 获取班级下的学生，按姓名排序
            const classStudents = students.filter(s => s.unit === cls.id)
                                         .sort((a, b) => a.name.localeCompare(b.name));
            sortedStudents.push(...classStudents);
        });
    });

    // 添加没有分配到班级的学生
    const unassignedStudents = students.filter(s => {
        const unit = units.find(u => u.id === s.unit);
        return !unit || unit.type !== 'class';
    }).sort((a, b) => a.name.localeCompare(b.name));

    sortedStudents.push(...unassignedStudents);

    return sortedStudents;
}

// 加载单位选项
function loadUnitOptions() {
    const units = dataManager.getUnits();
    const select = document.getElementById('studentUnit');
    select.innerHTML = '';

    units.forEach(unit => {
        if (unit.type === 'class' && dataManager.hasPermission(unit.id)) {
            const option = document.createElement('option');
            option.value = unit.id;
            option.textContent = unit.name;
            select.appendChild(option);
        }
    });
}

// 打开添加学生模态框
function openAddModal() {
    document.getElementById('modalTitle').textContent = '添加学生';
    document.getElementById('studentForm').reset();
    document.getElementById('studentId').removeAttribute('readonly');
    document.getElementById('absentFields').style.display = 'none';
    loadUnitOptions();
    showModal('addModal');
}

// 打开编辑学生模态框
function openEditModal(studentId) {
    const students = dataManager.getStudents();
    const student = students.find(s => s.id === studentId);

    if (!student) {
        utils.showMessage('学生信息不存在', 'error');
        return;
    }

    document.getElementById('modalTitle').textContent = '编辑学生';
    document.getElementById('studentName').value = student.name;
    document.getElementById('studentId').value = student.id;
    document.getElementById('studentId').setAttribute('readonly', true);
    document.getElementById('studentIdNumber').value = student.idNumber;
    document.getElementById('studentWorkDate').value = student.workDate;
    document.getElementById('studentType').value = student.type;
    document.getElementById('studentMajor').value = student.major;
    document.getElementById('studentStatus').value = student.status;
    document.getElementById('studentReason').value = student.reason || '';
    document.getElementById('studentLocation').value = student.location || '';
    document.getElementById('studentLocationType').value = student.locationType || 'internal';
    document.getElementById('studentSelfContact').value = student.contacts.self || '';
    document.getElementById('studentParentContact').value = student.contacts.parent || '';

    loadUnitOptions();
    document.getElementById('studentUnit').value = student.unit;

    toggleAbsentFields();
    showModal('addModal');
}

// 切换不在位字段显示
function toggleAbsentFields() {
    const status = document.getElementById('studentStatus').value;
    const absentFields = document.getElementById('absentFields');

    if (status === 'absent') {
        absentFields.style.display = 'block';
    } else {
        absentFields.style.display = 'none';
    }
}

// 表单验证管理类
class FormValidator {
    constructor() {
        this.rules = {};
        this.errorMessages = {};
    }

    // 添加验证规则
    addRule(fieldId, rule, message) {
        if (!this.rules[fieldId]) {
            this.rules[fieldId] = [];
        }
        this.rules[fieldId].push({ rule, message });
    }

    // 验证单个字段
    validateField(fieldId, value) {
        const rules = this.rules[fieldId] || [];
        for (const { rule, message } of rules) {
            if (!rule(value)) {
                return { valid: false, message };
            }
        }
        return { valid: true };
    }

    // 验证所有字段
    validateForm(formData) {
        const errors = {};
        let isValid = true;

        for (const [fieldId, value] of Object.entries(formData)) {
            const result = this.validateField(fieldId, value);
            if (!result.valid) {
                errors[fieldId] = result.message;
                isValid = false;
            }
        }

        return { isValid, errors };
    }

    // 显示字段错误
    showFieldError(fieldId, message) {
        const field = document.getElementById(fieldId);
        if (!field) return;

        // 移除之前的错误样式
        field.classList.remove('error');
        const existingError = field.parentNode.querySelector('.field-error');
        if (existingError) {
            existingError.remove();
        }

        if (message) {
            // 添加错误样式
            field.classList.add('error');

            // 添加错误消息
            const errorElement = document.createElement('div');
            errorElement.className = 'field-error';
            errorElement.textContent = message;
            field.parentNode.appendChild(errorElement);
        }
    }

    // 清除字段错误
    clearFieldError(fieldId) {
        this.showFieldError(fieldId, null);
    }

    // 清除所有错误
    clearAllErrors() {
        document.querySelectorAll('.error').forEach(field => {
            field.classList.remove('error');
        });
        document.querySelectorAll('.field-error').forEach(error => {
            error.remove();
        });
    }
}

// 创建表单验证器
const formValidator = new FormValidator();

// 设置学生表单验证规则
function setupStudentFormValidation() {
    formValidator.addRule('studentName',
        value => value && value.trim().length >= 2,
        '姓名至少需要2个字符');

    formValidator.addRule('studentId',
        value => value && /^[A-Za-z0-9]{6,20}$/.test(value.trim()),
        '学号/工号应为6-20位字母或数字');

    formValidator.addRule('studentIdNumber',
        value => value && /^[1-9]\d{5}(18|19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}[\dXx]$/.test(value.trim()),
        '请输入有效的身份证号');

    formValidator.addRule('studentMajor',
        value => value && value.trim().length >= 2,
        '专业名称至少需要2个字符');

    formValidator.addRule('studentSelfContact',
        value => !value || /^1[3-9]\d{9}$/.test(value.trim()),
        '请输入有效的手机号码');

    formValidator.addRule('studentParentContact',
        value => !value || /^1[3-9]\d{9}$/.test(value.trim()),
        '请输入有效的手机号码');

    // 添加实时验证
    const fields = ['studentName', 'studentId', 'studentIdNumber', 'studentMajor', 'studentSelfContact', 'studentParentContact'];
    fields.forEach(fieldId => {
        const field = document.getElementById(fieldId);
        if (field) {
            field.addEventListener('blur', () => {
                const result = formValidator.validateField(fieldId, field.value);
                if (!result.valid) {
                    formValidator.showFieldError(fieldId, result.message);
                } else {
                    formValidator.clearFieldError(fieldId);
                }
            });

            field.addEventListener('input', () => {
                // 清除错误状态（但不立即验证）
                if (field.classList.contains('error')) {
                    formValidator.clearFieldError(fieldId);
                }
            });
        }
    });
}

// 保存学生信息
async function saveStudent() {
    const formData = {
        studentName: document.getElementById('studentName').value.trim(),
        studentId: document.getElementById('studentId').value.trim(),
        studentIdNumber: document.getElementById('studentIdNumber').value.trim(),
        studentWorkDate: document.getElementById('studentWorkDate').value,
        studentType: document.getElementById('studentType').value,
        studentMajor: document.getElementById('studentMajor').value.trim(),
        studentUnit: document.getElementById('studentUnit').value,
        studentStatus: document.getElementById('studentStatus').value,
        studentReason: document.getElementById('studentReason').value,
        studentLocation: document.getElementById('studentLocation').value.trim(),
        studentLocationType: document.getElementById('studentLocationType').value,
        studentSelfContact: document.getElementById('studentSelfContact').value.trim(),
        studentParentContact: document.getElementById('studentParentContact').value.trim()
    };

    // 验证表单
    const validation = formValidator.validateForm(formData);
    if (!validation.isValid) {
        // 显示所有错误
        for (const [fieldId, message] of Object.entries(validation.errors)) {
            formValidator.showFieldError(fieldId, message);
        }
        utils.showMessage('请修正表单中的错误', 'error');
        return;
    }

    // 检查必填字段
    const requiredFields = ['studentName', 'studentId', 'studentIdNumber', 'studentWorkDate', 'studentType', 'studentMajor', 'studentUnit'];
    const missingFields = requiredFields.filter(field => !formData[field]);

    if (missingFields.length > 0) {
        missingFields.forEach(field => {
            formValidator.showFieldError(field, '此字段为必填项');
        });
        utils.showMessage('请填写所有必填字段', 'error');
        return;
    }

    // 检查学号是否重复
    const students = dataManager.getStudents();
    const existingStudent = students.find(s => s.id === formData.studentId);
    const isEditing = document.getElementById('studentId').hasAttribute('readonly');

    if (existingStudent && !isEditing) {
        formValidator.showFieldError('studentId', '该学号已存在');
        utils.showMessage('学号不能重复', 'error');
        return;
    }

    const studentData = {
        name: formData.studentName,
        id: formData.studentId,
        idNumber: formData.studentIdNumber,
        workDate: formData.studentWorkDate,
        type: formData.studentType,
        major: formData.studentMajor,
        status: formData.studentStatus,
        reason: formData.studentStatus === 'absent' ? formData.studentReason : '',
        location: formData.studentStatus === 'absent' ? formData.studentLocation : '',
        locationType: formData.studentStatus === 'absent' ? formData.studentLocationType : 'internal',
        contacts: {
            self: formData.studentSelfContact,
            parent: formData.studentParentContact
        },
        unit: formData.studentUnit
    };

    try {
        // 显示保存中状态
        const saveBtn = document.querySelector('#addModal .btn-primary');
        const originalText = saveBtn.textContent;
        saveBtn.textContent = '保存中...';
        saveBtn.disabled = true;

        const existingIndex = students.findIndex(s => s.id === formData.studentId);

        if (existingIndex >= 0) {
            students[existingIndex] = studentData;
        } else {
            students.push(studentData);
        }

        const success = await dataManager.saveStudents(students);

        if (success) {
            formValidator.clearAllErrors();
            closeModal('addModal');
            renderStudentTable();
            updateStats();
            initCharts(currentUnit);
            utils.showMessage('学生信息保存成功', 'success');
        } else {
            utils.showMessage('保存失败，请重试', 'error');
        }
    } catch (error) {
        console.error('保存学生信息失败:', error);
        utils.showMessage('保存失败，请检查网络连接', 'error');
    } finally {
        // 恢复按钮状态
        const saveBtn = document.querySelector('#addModal .btn-primary');
        if (saveBtn) {
            saveBtn.textContent = '保存';
            saveBtn.disabled = false;
        }
    }
}

// 删除学生
async function deleteStudent(studentId, event) {
    const deleteBtn = event.target.closest('.action-btn');
    if (deleteBtn && deleteBtn.disabled) return;

    const students = dataManager.getStudents();
    const student = students.find(s => s.id === studentId);

    if (!student) {
        utils.error('学生信息不存在');
        return;
    }

    const confirmed = await utils.confirm(
        `确定要删除学生 "${student.name}" 吗？此操作不可撤销。`,
        {
            confirmText: '删除',
            confirmClass: 'btn-danger'
        }
    );

    if (!confirmed) {
        return;
    }

    if (deleteBtn) {
        deleteBtn.disabled = true;
        deleteBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
    }

    const filteredStudents = students.filter(s => s.id !== studentId);

    try {
        const success = await dataManager.saveStudents(filteredStudents);
        if (success) {
            // 成功后，dataManager中的数据已更新，直接重新渲染即可
            await dataManager.refresh(); // 确保从后端拉取最新数据
            utils.success(`学生 "${student.name}" 删除成功`);
        } else {
            utils.error('删除失败，请重试');
            // 恢复按钮状态
            if (deleteBtn && !deleteBtn.closest('tbody').innerHTML.includes('fa-spinner')) {
                deleteBtn.disabled = false;
                deleteBtn.innerHTML = '<i class="fas fa-trash"></i>';
            }
        }
    } catch (error) {
        console.error('删除学生失败:', error);
        utils.error('删除失败，请检查网络连接');
        // 恢复按钮状态
        if (deleteBtn && !deleteBtn.closest('tbody').innerHTML.includes('fa-spinner')) {
            deleteBtn.disabled = false;
            deleteBtn.innerHTML = '<i class="fas fa-trash"></i>';
        }
    }
    // 注意：成功时不需要在finally中恢复按钮状态，因为表格已经重新渲染
}

// 全选/取消全选
function toggleSelectAll() {
    const selectAll = document.getElementById('selectAll');
    const checkboxes = document.querySelectorAll('.student-checkbox');

    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
    });
}

// 批量删除学生
async function batchDelete() {
    const batchDeleteBtn = document.querySelector('button[onclick="batchDelete()"]');
    if (batchDeleteBtn && batchDeleteBtn.disabled) return;

    const checkboxes = document.querySelectorAll('.student-checkbox:checked');

    if (checkboxes.length === 0) {
        utils.warning('请选择要删除的学生');
        return;
    }

    const confirmed = await utils.confirm(
        `确定要删除选中的 ${checkboxes.length} 个学生吗？此操作不可撤销。`,
        {
            confirmText: '批量删除',
            confirmClass: 'btn-danger'
        }
    );

    if (!confirmed) {
        return;
    }

    let originalBtnHTML = '';
    if (batchDeleteBtn) {
        originalBtnHTML = batchDeleteBtn.innerHTML;
        batchDeleteBtn.disabled = true;
        batchDeleteBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 删除中...';
    }

    const selectedIds = Array.from(checkboxes).map(cb => cb.value);
    const students = dataManager.getStudents();
    const filteredStudents = students.filter(s => !selectedIds.includes(s.id));

    try {
        const success = await dataManager.saveStudents(filteredStudents);
        if (success) {
            await dataManager.refresh(); // 刷新数据和UI
            document.getElementById('selectAll').checked = false;
            utils.success(`成功删除 ${checkboxes.length} 个学生`);

            // 成功后手动恢复按钮状态，因为按钮可能不在重新渲染的表格中
            setTimeout(() => {
                const currentBatchBtn = document.querySelector('button[onclick="batchDelete()"]');
                if (currentBatchBtn) {
                    currentBatchBtn.disabled = false;
                    currentBatchBtn.innerHTML = originalBtnHTML;
                }
            }, 100);
        } else {
            utils.error('批量删除失败，请重试');
            // 恢复按钮状态
            if (batchDeleteBtn) {
                batchDeleteBtn.disabled = false;
                batchDeleteBtn.innerHTML = originalBtnHTML;
            }
        }
    } catch (error) {
        console.error('批量删除失败:', error);
        utils.error('批量删除失败，请检查网络连接');
        // 恢复按钮状态
        if (batchDeleteBtn) {
            batchDeleteBtn.disabled = false;
            batchDeleteBtn.innerHTML = originalBtnHTML;
        }
    }
}

// 打开导入模态框
function openImportModal() {
    showModal('importModal');
}

// Excel导入功能
async function importExcel() {
    const importBtn = document.querySelector('#importModal .btn-primary');
    if (importBtn && importBtn.disabled) return;

    const fileInput = document.getElementById('excelFile');
    const file = fileInput.files[0];

    if (!file) {
        utils.showMessage('请选择一个文件', 'error');
        return;
    }

    let originalBtnHTML = '';
    if (importBtn) {
        originalBtnHTML = importBtn.innerHTML;
        importBtn.disabled = true;
        importBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 导入中...';
    }

    try {
        const result = await api.uploadExcel(file);

        if (result.success && result.data) {
            const importedData = result.data;
            // 总是从 dataManager 获取最新的学生列表
            const students = dataManager.getStudents();
            const units = dataManager.getUnits();
            let addedCount = 0;
            let updatedCount = 0;
            let skippedCount = 0;

            importedData.forEach(row => {
                const studentId = (row['学号/工号'] || '').toString().trim();
                const studentName = String(row['姓名'] || '').trim();

                // 必填字段检查
                if (!studentId || !studentName) {
                    skippedCount++;
                    return;
                }

                // 处理日期格式 - 如果是Excel序列号，转换为日期
                let workDate = String(row['参加工作时间'] || '').trim();
                if (workDate && !isNaN(workDate) && workDate.includes('.')) {
                    // Excel日期序列号转换
                    const excelDate = parseFloat(workDate);
                    const jsDate = new Date((excelDate - 25569) * 86400 * 1000);
                    workDate = jsDate.toISOString().split('T')[0]; // 格式化为 YYYY-MM-DD
                }

                // 处理单位匹配
                let unitId = String(row['单位'] || '').trim();
                if (unitId) {
                    // 尝试通过单位名称找到对应的单位ID
                    const matchedUnit = units.find(u => u.name === unitId);
                    if (matchedUnit) {
                        unitId = matchedUnit.id;
                    } else {
                        // 如果找不到匹配的单位，使用当前用户的单位
                        unitId = currentUser.unit;
                    }
                } else {
                    unitId = currentUser.unit;
                }

                const studentData = {
                    name: studentName,
                    id: studentId,
                    idNumber: String(row['身份证号'] || '').trim(),
                    workDate: workDate,
                    type: String(row['人员类别'] || 'A类').trim(),
                    major: String(row['专业'] || '').trim(),
                    unit: unitId,
                    status: String(row['在位状态'] || '').trim() === '不在位' ? 'absent' : 'present',
                    reason: String(row['外出原因'] || '').trim(),
                    location: String(row['外出地点'] || '').trim(),
                    locationType: String(row['位置类型'] || '').trim() === '单位内' ? 'internal' : 'external',
                    contacts: {
                        self: String(row['本人电话'] || '').trim(),
                        parent: String(row['父母电话'] || '').trim()
                    }
                };

                const existingIndex = students.findIndex(s => s.id === studentData.id);
                if (existingIndex >= 0) {
                    students[existingIndex] = { ...students[existingIndex], ...studentData };
                    updatedCount++;
                } else {
                    students.push(studentData);
                    addedCount++;
                }
            });

            const success = await dataManager.saveStudents(students);
            if (success) {
                await dataManager.refresh(); // 刷新数据和UI
                closeModal('importModal');
                let message = `导入成功！新增 ${addedCount} 人，更新 ${updatedCount} 人`;
                if (skippedCount > 0) {
                    message += `，跳过 ${skippedCount} 条无效记录`;
                }
                utils.showMessage(message, 'success');
            } else {
                utils.showMessage('保存导入数据失败', 'error');
            }
        } else {
            utils.showMessage(result.message || '文件解析失败，请检查文件格式和内容', 'error');
        }
    } catch (error) {
        console.error('导入失败:', error);
        utils.showMessage('导入失败，请检查网络连接或文件内容', 'error');
    } finally {
        // 清空文件输入，以便可以再次选择同一个文件
        if (fileInput) {
            fileInput.value = '';
        }
        const uploadBox = document.querySelector('.upload-box p');
        if (uploadBox) {
            uploadBox.textContent = '点击选择CSV文件';
        }

        if (importBtn) {
            importBtn.disabled = false;
            importBtn.innerHTML = originalBtnHTML;
        }
    }
}

// 批量导出Excel功能
function exportExcel() {
    try {
        const students = chartManager.getStudentsForUnit(currentUnit);
        const units = dataManager.getUnits();

        if (!students || students.length === 0) {
            utils.showMessage('当前没有数据可导出。', 'warning');
            return;
        }

        const headers = [
            '姓名', '学号/工号', '身份证号', '参加工作时间', '人员类别', '专业',
            '单位', '在位状态', '位置类型', '外出原因', '外出地点', '本人电话', '父母电话'
        ];

        const csvRows = [headers.join(',')];

        students.forEach(student => {
            const unit = units.find(u => u.id === student.unit);
            const unitName = unit ? unit.name : '未知单位';

            const row = [
                `"${student.name}"`,
                `"${student.id}"`,
                `"\t${student.idNumber}"`,
                `"${student.workDate}"`,
                `"${student.type}"`,
                `"${student.major}"`,
                `"${unitName}"`,
                `"${student.status === 'present' ? '在位' : '不在位'}"`,
                `"${student.locationType === 'internal' ? '单位内' : '单位外'}"`,
                `"${student.reason || ''}"`,
                `"${student.location || ''}"`,
                `"\t${student.contacts.self}"`,
                `"\t${student.contacts.parent}"`
            ];
            csvRows.push(row.join(','));
        });

        const csvContent = csvRows.join('\n');
        const blob = new Blob([`\uFEFF${csvContent}`], { type: 'text/csv;charset=utf-8;' });

        const currentUnitName = getCurrentUnitName();
        const fileName = `${currentUnitName}_学生信息_${new Date().toISOString().slice(0, 10)}.csv`;

        const link = document.createElement("a");
        if (link.download !== undefined) {
            const url = URL.createObjectURL(blob);
            link.setAttribute("href", url);
            link.setAttribute("download", fileName);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        utils.showMessage(`成功导出 ${students.length} 条学生信息`, 'success');
    } catch (error) {
        console.error('导出失败:', error);
        utils.showMessage('导出失败，请重试', 'error');
    }
}

// 下载导入模板
function downloadTemplate() {
    try {
        // 创建模板数据
        const templateData = [
            {
                '姓名': '张三',
                '学号/工号': '2023001',
                '身份证号': '110101199001011234',
                '参加工作时间': '2023-09-01',
                '人员类别': 'A类',
                '专业': '计算机科学与技术',
                '单位': '计算机学院',
                '在位状态': '在位',
                '位置类型': '单位内',
                '外出原因': '',
                '外出地点': '',
                '本人电话': '13800138000',
                '父母电话': '13900139000'
            },
            {
                '姓名': '李四',
                '学号/工号': '2023002',
                '身份证号': '110101199002021234',
                '参加工作时间': '2023-09-01',
                '人员类别': 'B类',
                '专业': '软件工程',
                '单位': '计算机学院',
                '在位状态': '不在位',
                '位置类型': '单位外',
                '外出原因': '休假',
                '外出地点': '北京市',
                '本人电话': '13800138001',
                '父母电话': '13900139001'
            }
        ];

        // 使用SheetJS创建Excel文件
        const ws = XLSX.utils.json_to_sheet(templateData);
        const csvContent = XLSX.utils.sheet_to_csv(ws);
        const blob = new Blob([`\uFEFF${csvContent}`], { type: 'text/csv;charset=utf-8;' });

        const fileName = '学生信息导入模板.csv';

        const link = document.createElement("a");
        if (link.download !== undefined) {
            const url = URL.createObjectURL(blob);
            link.setAttribute("href", url);
            link.setAttribute("download", fileName);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }
        utils.showMessage('模板下载成功', 'success');
    } catch (error) {
        console.error('模板下载失败:', error);
        utils.showMessage('模板下载失败，请重试', 'error');
    }
}

// 获取当前单位名称
function getCurrentUnitName() {
    if (currentUnit === 'school') {
        return '全校';
    }

    const units = dataManager.getUnits();
    const unit = units.find(u => u.id === currentUnit);
    return unit ? unit.name : '未知单位';
}

// 编辑单位
function editUnit(unitId, event) {
    event.stopPropagation();

    const units = dataManager.getUnits();
    const unit = units.find(u => u.id === unitId);

    if (!unit) {
        utils.showMessage('单位不存在', 'error');
        return;
    }

    const newName = prompt('请输入新的单位名称:', unit.name);
    if (newName && newName.trim() && newName.trim() !== unit.name) {
        unit.name = newName.trim();

        dataManager.saveUnits(units).then(success => {
            if (success) {
                renderUnitTree();
                renderStudentTable();
                utils.showMessage('单位名称修改成功', 'success');
            } else {
                utils.showMessage('修改失败', 'error');
            }
        });
    }
}

// 删除单位
function deleteUnit(unitId, event) {
    event.stopPropagation();

    if (!utils.confirm('确定要删除这个单位吗？该单位下的所有学生数据也将被删除！')) {
        return;
    }

    const units = dataManager.getUnits();
    const students = dataManager.getStudents();

    // 找到要删除的单位及其子单位
    const deleteIds = [unitId];
    const unit = units.find(u => u.id === unitId);

    if (unit && unit.type === 'college') {
        const classes = units.filter(u => u.parent === unitId);
        classes.forEach(cls => deleteIds.push(cls.id));
    }

    // 删除单位
    const filteredUnits = units.filter(u => !deleteIds.includes(u.id));

    // 删除相关学生
    const filteredStudents = students.filter(s => !deleteIds.includes(s.unit));

    Promise.all([
        dataManager.saveUnits(filteredUnits),
        dataManager.saveStudents(filteredStudents)
    ]).then(([unitsSuccess, studentsSuccess]) => {
        if (unitsSuccess && studentsSuccess) {
            renderUnitTree();
            renderStudentTable();
            updateStats();
            initCharts(currentUnit);
            utils.showMessage('单位删除成功', 'success');
        } else {
            utils.showMessage('删除失败', 'error');
        }
    });
}

// 添加单位模态框
function openUnitModal() {
    document.getElementById('unitModalTitle').textContent = '添加单位';
    document.getElementById('unitForm').reset();
    document.getElementById('parentUnitGroup').style.display = 'none';
    loadParentUnits();
    showModal('addUnitModal');
}

// 切换上级单位显示
function toggleParentUnit() {
    const unitType = document.getElementById('unitType').value;
    const parentUnitGroup = document.getElementById('parentUnitGroup');

    if (unitType === 'class') {
        parentUnitGroup.style.display = 'block';
        loadParentUnits();
    } else {
        parentUnitGroup.style.display = 'none';
    }
}

// 加载上级单位选项
function loadParentUnits() {
    const units = dataManager.getUnits();
    const parentSelect = document.getElementById('parentUnit');
    parentSelect.innerHTML = '<option value="">请选择上级单位</option>';

    // 班级的上级单位是学院
    const colleges = units.filter(u => u.type === 'college');
    colleges.forEach(college => {
        if (dataManager.hasPermission(college.id)) {
            const option = document.createElement('option');
            option.value = college.id;
            option.textContent = college.name;
            parentSelect.appendChild(option);
        }
    });
}

// 保存单位
async function saveUnit() {
    const name = document.getElementById('unitName').value.trim();
    const type = document.getElementById('unitType').value;
    const parent = document.getElementById('parentUnit').value;
    const description = document.getElementById('unitDescription').value.trim();

    // 验证必填字段
    if (!name || !type) {
        utils.error('请填写单位名称和类型');
        return;
    }

    if (type === 'class' && !parent) {
        utils.error('班级必须选择上级学院');
        return;
    }

    // 检查名称是否重复
    const units = dataManager.getUnits();
    const existingUnit = units.find(u => u.name === name && u.type === type);
    if (existingUnit) {
        utils.error('该单位名称已存在');
        return;
    }

    // 生成单位ID
    const unitId = utils.generateId();

    const unitData = {
        id: unitId,
        name: name,
        type: type,
        parent: type === 'class' ? parent : (type === 'college' ? 'school' : ''),
        description: description,
        createdAt: new Date().toISOString()
    };

    try {
        // 显示保存中状态
        const saveBtn = document.querySelector('#addUnitModal .btn-primary');
        const originalText = saveBtn.textContent;
        saveBtn.textContent = '保存中...';
        saveBtn.disabled = true;

        units.push(unitData);
        const success = await dataManager.saveUnits(units);

        if (success) {
            closeModal('addUnitModal');

            // 如果添加的是班级，自动展开其所属学院
            if (type === 'class' && parent) {
                unitTreeManager.expandedNodes.add(parent);
            }

            // 刷新数据和界面
            await dataManager.refresh();
            renderUnitTree();
            loadUnitOptions(); // 更新学生表单中的单位选项

            utils.success(`${type === 'college' ? '学院' : '班级'} "${name}" 添加成功`);
        } else {
            utils.error('保存失败，请重试');
        }
    } catch (error) {
        console.error('保存单位失败:', error);
        utils.error('保存失败，请检查网络连接');
    } finally {
        // 恢复按钮状态
        const saveBtn = document.querySelector('#addUnitModal .btn-primary');
        if (saveBtn) {
            saveBtn.textContent = '保存';
            saveBtn.disabled = false;
        }
    }
}

// 添加账号模态框
function openAccountModal() {
    document.getElementById('accountForm').reset();
    document.getElementById('accountUnit').innerHTML = '<option value="">请先选择账号级别</option>';
    showModal('addAccountModal');
}

// 加载账号管理单位选项
function loadAccountUnits() {
    const level = document.getElementById('accountLevel').value;
    const unitSelect = document.getElementById('accountUnit');
    unitSelect.innerHTML = '<option value="">请选择管理单位</option>';

    if (!level) return;

    const units = dataManager.getUnits();

    if (level === 'school') {
        const school = units.find(u => u.type === 'school');
        if (school) {
            const option = document.createElement('option');
            option.value = school.id;
            option.textContent = school.name;
            unitSelect.appendChild(option);
        }
    } else if (level === 'college') {
        const colleges = units.filter(u => u.type === 'college');
        colleges.forEach(college => {
            const option = document.createElement('option');
            option.value = college.id;
            option.textContent = college.name;
            unitSelect.appendChild(option);
        });
    } else if (level === 'class') {
        const classes = units.filter(u => u.type === 'class');
        classes.forEach(cls => {
            const option = document.createElement('option');
            option.value = cls.id;
            option.textContent = cls.name;
            unitSelect.appendChild(option);
        });
    }
}

// 保存账号
async function saveAccount() {
    const username = document.getElementById('accountUsername').value.trim();
    const password = document.getElementById('accountPassword').value.trim();
    const level = document.getElementById('accountLevel').value;
    const unit = document.getElementById('accountUnit').value;
    const description = document.getElementById('accountDescription').value.trim();

    // 验证必填字段
    if (!username || !password || !level || !unit) {
        utils.error('请填写所有必填字段');
        return;
    }

    // 验证用户名格式
    if (!/^[a-zA-Z0-9_]{3,20}$/.test(username)) {
        utils.error('用户名应为3-20位字母、数字或下划线');
        return;
    }

    // 验证密码长度
    if (password.length < 6) {
        utils.error('密码至少需要6位字符');
        return;
    }

    // 检查用户名是否重复
    const accounts = dataManager.getAccounts();
    const existingAccount = accounts.find(acc => acc.username === username);
    if (existingAccount) {
        utils.error('该用户名已存在');
        return;
    }

    const accountData = {
        username: username,
        password: password,
        level: level,
        unit: unit,
        description: description,
        createdAt: new Date().toISOString()
    };

    try {
        // 显示保存中状态
        const saveBtn = document.querySelector('#addAccountModal .btn-primary');
        const originalText = saveBtn.textContent;
        saveBtn.textContent = '保存中...';
        saveBtn.disabled = true;

        accounts.push(accountData);
        const success = await dataManager.saveAccounts(accounts);

        if (success) {
            closeModal('addAccountModal');
            loadAccountsTable(); // 重新加载账号表格
            utils.success(`账号 "${username}" 添加成功`);
        } else {
            utils.error('保存失败，请重试');
        }
    } catch (error) {
        console.error('保存账号失败:', error);
        utils.error('保存失败，请检查网络连接');
    } finally {
        // 恢复按钮状态
        const saveBtn = document.querySelector('#addAccountModal .btn-primary');
        if (saveBtn) {
            saveBtn.textContent = '保存';
            saveBtn.disabled = false;
        }
    }
}

// 打开修改密码模态框
function openChangePasswordModal() {
    const accounts = dataManager.getAccounts();
    const select = document.getElementById('changePasswordUsername');

    // 清空选项
    select.innerHTML = '<option value="">请选择要修改密码的账号</option>';

    // 添加账号选项
    accounts.forEach(account => {
        const option = document.createElement('option');
        option.value = account.username;
        option.textContent = `${account.username} (${getLevelText(account.level)})`;
        select.appendChild(option);
    });

    // 重置表单
    document.getElementById('changePasswordForm').reset();
    showModal('changePasswordModal');
}

// 修改密码
async function changePassword() {
    const username = document.getElementById('changePasswordUsername').value;
    const newPassword = document.getElementById('newPassword').value.trim();
    const confirmPassword = document.getElementById('confirmPassword').value.trim();

    if (!username || !newPassword || !confirmPassword) {
        utils.error('请填写所有字段');
        return;
    }

    if (newPassword !== confirmPassword) {
        utils.error('两次输入的密码不一致');
        return;
    }

    if (newPassword.length < 6) {
        utils.error('密码长度不能少于6位');
        return;
    }

    try {
        // 显示保存中状态
        const saveBtn = document.querySelector('#changePasswordModal .btn-primary');
        const originalText = saveBtn.textContent;
        saveBtn.textContent = '修改中...';
        saveBtn.disabled = true;

        const accounts = dataManager.getAccounts();
        const accountIndex = accounts.findIndex(acc => acc.username === username);

        if (accountIndex === -1) {
            utils.error('账号不存在');
            return;
        }

        // 更新密码
        accounts[accountIndex].password = newPassword;
        accounts[accountIndex].updatedAt = new Date().toISOString();

        const success = await dataManager.saveAccounts(accounts);

        if (success) {
            closeModal('changePasswordModal');
            utils.success(`账号 "${username}" 密码修改成功`);
        } else {
            utils.error('密码修改失败，请重试');
        }
    } catch (error) {
        console.error('修改密码失败:', error);
        utils.error('修改密码失败，请检查网络连接');
    } finally {
        // 恢复按钮状态
        const saveBtn = document.querySelector('#changePasswordModal .btn-primary');
        if (saveBtn) {
            saveBtn.textContent = '修改密码';
            saveBtn.disabled = false;
        }
    }
}

// 获取级别文本
function getLevelText(level) {
    const levelMap = {
        'school': '学校管理员',
        'college': '学院管理员',
        'class': '班级管理员'
    };
    return levelMap[level] || '未知';
}

// 系统设置功能
function openSettingsModal() {
    if (!currentUser) {
        utils.warning('请先登录');
        return;
    }

    loadSettingsData();
    showModal('settingsModal');
}

// 切换设置标签
function switchTab(tabName) {
    // 隐藏所有标签内容
    document.querySelectorAll('.tab-content').forEach(tab => {
        tab.classList.remove('active');
    });

    // 移除所有按钮的激活状态
    document.querySelectorAll('.tab-btn').forEach(btn => {
        btn.classList.remove('active');
    });

    // 显示选中的标签
    document.getElementById(tabName + 'Tab').classList.add('active');
    event.target.classList.add('active');
}

// 加载设置数据
function loadSettingsData() {
    // 根据用户权限显示/隐藏设置标签
    updateSettingsTabsVisibility();

    // 加载图表设置
    loadChartSettings();

    // 只有学校管理员才能加载单位管理和账号管理
    if (currentUser && currentUser.level === 'school') {
        // 加载单位管理表格
        loadUnitsTable();

        // 加载账号管理表格
        loadAccountsTable();
    }

    // 加载背景设置
    loadBackgroundSettings();
}

// 根据用户权限更新设置标签可见性
function updateSettingsTabsVisibility() {
    const isSchoolAdmin = currentUser && currentUser.level === 'school';

    // 单位管理标签
    const unitsTabBtn = document.querySelector('[onclick="switchTab(\'units\')"]');
    const unitsTab = document.getElementById('unitsTab');
    if (unitsTabBtn && unitsTab) {
        unitsTabBtn.style.display = isSchoolAdmin ? 'block' : 'none';
        if (!isSchoolAdmin) {
            unitsTab.classList.remove('active');
        }
    }

    // 账号管理标签
    const accountsTabBtn = document.querySelector('[onclick="switchTab(\'accounts\')"]');
    const accountsTab = document.getElementById('accountsTab');
    if (accountsTabBtn && accountsTab) {
        accountsTabBtn.style.display = isSchoolAdmin ? 'block' : 'none';
        if (!isSchoolAdmin) {
            accountsTab.classList.remove('active');
        }
    }

    // 如果不是学校管理员，默认激活图表设置标签
    if (!isSchoolAdmin) {
        document.getElementById('chartsTab').classList.add('active');
        document.querySelector('[onclick="switchTab(\'charts\')"]').classList.add('active');
    }
}

// 加载图表设置
function loadChartSettings() {
    const settings = JSON.parse(localStorage.getItem('chartSettings') || '{}');

    document.getElementById('showTotalChart').checked = settings.showTotalChart !== false;
    document.getElementById('showReasonChart').checked = settings.showReasonChart !== false;
    document.getElementById('showATypeChart').checked = settings.showATypeChart !== false;
    document.getElementById('showAReasonChart').checked = settings.showAReasonChart !== false;
    document.getElementById('showBTypeChart').checked = settings.showBTypeChart !== false;
    document.getElementById('showBReasonChart').checked = settings.showBReasonChart !== false;
}

// 加载单位管理表格
function loadUnitsTable() {
    const tbody = document.getElementById('unitsTableBody');
    tbody.innerHTML = '';

    const units = dataManager.getUnits().filter(u => u.type !== 'school');

    units.forEach(unit => {
        const parentUnit = dataManager.getUnits().find(u => u.id === unit.parent);
        const row = tbody.insertRow();
        row.innerHTML = `
            <td>${unit.name}</td>
            <td>${unit.type === 'college' ? '学院' : '班级'}</td>
            <td>${parentUnit ? parentUnit.name : '-'}</td>
            <td>${unit.createdAt ? new Date(unit.createdAt).toLocaleDateString() : '-'}</td>
            <td>
                <div class="action-buttons">
                    <button class="action-btn edit-btn" onclick="editUnitInTable('${unit.id}')">编辑</button>
                    <button class="action-btn delete-btn" onclick="deleteUnitInTable('${unit.id}')">删除</button>
                </div>
            </td>
        `;
    });
}

// 在表格中编辑单位
function editUnitInTable(unitId) {
    // 模拟一个事件对象，因为editUnit需要它
    const mockEvent = { stopPropagation: () => {} };
    editUnit(unitId, mockEvent);
}

// 在表格中删除单位
function deleteUnitInTable(unitId) {
    // 模拟一个事件对象，因为deleteUnit需要它
    const mockEvent = { stopPropagation: () => {} };
    deleteUnit(unitId, mockEvent);
}
// 加载账号管理表格
function loadAccountsTable() {
    const tbody = document.getElementById('accountsTableBody');
    tbody.innerHTML = '';

    const accounts = dataManager.getAccounts();
    const units = dataManager.getUnits();

    accounts.forEach(account => {
        const unit = units.find(u => u.id === account.unit);
        const levelText = {
            'school': '学校级管理员',
            'college': '学院级管理员',
            'class': '班级管理员'
        }[account.level] || account.level;

        const row = tbody.insertRow();
        row.innerHTML = `
            <td>${account.username}</td>
            <td>${levelText}</td>
            <td>${unit ? unit.name : '-'}</td>
            <td>${account.createdAt ? new Date(account.createdAt).toLocaleDateString() : '-'}</td>
            <td>
                <div class="action-buttons">
                    <button class="action-btn edit-btn" onclick="editAccountPassword('${account.username}')" title="修改密码">
                        <i class="fas fa-key"></i>
                    </button>
                    <button class="action-btn delete-btn" onclick="deleteAccountInTable('${account.username}')" title="删除账号">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        `;
    });
}

// 编辑账号密码
function editAccountPassword(username) {
    // 打开修改密码模态框并预选账号
    openChangePasswordModal();
    document.getElementById('changePasswordUsername').value = username;
}

// 删除账号
async function deleteAccountInTable(username) {
    const confirmed = await utils.confirm(
        `确定要删除账号 "${username}" 吗？此操作不可撤销。`,
        {
            confirmText: '删除账号',
            confirmClass: 'btn-danger'
        }
    );

    if (!confirmed) {
        return;
    }

    try {
        const accounts = dataManager.getAccounts();
        const filteredAccounts = accounts.filter(acc => acc.username !== username);

        const success = await dataManager.saveAccounts(filteredAccounts);
        if (success) {
            loadUnitsTable();
loadAccountsTable(); // 重新加载表格
            utils.success(`账号 "${username}" 删除成功`);
        } else {
            utils.error('删除失败，请重试');
        }
    } catch (error) {
        console.error('删除账号失败:', error);
        utils.error('删除失败，请检查网络连接');
    }
}

// 加载背景设置
function loadBackgroundSettings() {
    const loginBg = localStorage.getItem('loginBackground');
    const mainBg = localStorage.getItem('mainBackground');

    if (loginBg) {
        document.getElementById('loginBgPreview').src = loginBg;
        document.getElementById('loginBgPreview').style.display = 'block';
        document.querySelector('#loginBgPreview').parentNode.querySelector('.upload-placeholder').style.display = 'none';
    }

    if (mainBg) {
        document.getElementById('mainBgPreview').src = mainBg;
        document.getElementById('mainBgPreview').style.display = 'block';
        document.querySelector('#mainBgPreview').parentNode.querySelector('.upload-placeholder').style.display = 'none';
    }
}

// 保存设置
function saveSettings() {
    // 保存图表设置
    const chartSettings = {
        showTotalChart: document.getElementById('showTotalChart').checked,
        showReasonChart: document.getElementById('showReasonChart').checked,
        showATypeChart: document.getElementById('showATypeChart').checked,
        showAReasonChart: document.getElementById('showAReasonChart').checked,
        showBTypeChart: document.getElementById('showBTypeChart').checked,
        showBReasonChart: document.getElementById('showBReasonChart').checked
    };

    localStorage.setItem('chartSettings', JSON.stringify(chartSettings));

    // 应用图表设置
    applyChartSettings();

    utils.success('设置保存成功');
    closeModal('settingsModal');
}

// 应用图表设置
function applyChartSettings() {
    const settings = JSON.parse(localStorage.getItem('chartSettings') || '{}');

    document.getElementById('totalChart').parentNode.style.display =
        settings.showTotalChart !== false ? 'block' : 'none';
    document.getElementById('reasonChart').parentNode.style.display =
        settings.showReasonChart !== false ? 'block' : 'none';
    document.getElementById('aTypeChart').parentNode.style.display =
        settings.showATypeChart !== false ? 'block' : 'none';
    document.getElementById('aReasonChart').parentNode.style.display =
        settings.showAReasonChart !== false ? 'block' : 'none';
    document.getElementById('bTypeChart').parentNode.style.display =
        settings.showBTypeChart !== false ? 'block' : 'none';
    document.getElementById('bReasonChart').parentNode.style.display =
        settings.showBReasonChart !== false ? 'block' : 'none';
}

// 背景图片上传功能
document.addEventListener('DOMContentLoaded', function() {
    // 登录背景上传
    const loginBgFile = document.getElementById('loginBgFile');
    if (loginBgFile) {
        loginBgFile.addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                uploadBackgroundImage(file, 'login');
            }
        });
    }

    // 主页背景上传
    const mainBgFile = document.getElementById('mainBgFile');
    if (mainBgFile) {
        mainBgFile.addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                uploadBackgroundImage(file, 'main');
            }
        });
    }
});

// 上传背景图片
async function uploadBackgroundImage(file, type) {
    const formData = new FormData();
    formData.append('image', file);

    try {
        const response = await fetch('/api/upload-image', {
            method: 'POST',
            body: formData
        });

        const result = await response.json();

        if (result.success) {
            const imageUrl = result.imageUrl;

            // 保存到localStorage
            if (type === 'login') {
                localStorage.setItem('loginBackground', imageUrl);
                document.getElementById('loginBgPreview').src = imageUrl;
                document.getElementById('loginBgPreview').style.display = 'block';
                document.querySelector('#loginBgPreview').parentNode.querySelector('.upload-placeholder').style.display = 'none';

                // 应用到登录页面
                applyLoginBackground();
            } else if (type === 'main') {
                localStorage.setItem('mainBackground', imageUrl);
                document.getElementById('mainBgPreview').src = imageUrl;
                document.getElementById('mainBgPreview').style.display = 'block';
                document.querySelector('#mainBgPreview').parentNode.querySelector('.upload-placeholder').style.display = 'none';

                // 应用到主页面
                applyMainBackground();
            }

            utils.success('背景图片上传成功');
        } else {
            utils.error('图片上传失败：' + result.message);
        }
    } catch (error) {
        console.error('上传失败:', error);
        utils.error('图片上传失败，请检查网络连接');
    }
}

// 应用登录背景
function applyLoginBackground() {
    const loginBg = localStorage.getItem('loginBackground');
    if (loginBg) {
        const loginModal = document.getElementById('loginModal');
        if (loginModal) {
            loginModal.style.backgroundImage = `url(${loginBg})`;
            loginModal.style.backgroundSize = 'cover';
            loginModal.style.backgroundPosition = 'center';
        }
    }
}

// 应用主页背景
function applyMainBackground() {
    const mainBg = localStorage.getItem('mainBackground');
    if (mainBg) {
        document.body.style.backgroundImage = `url(${mainBg})`;
        document.body.style.backgroundSize = 'cover';
        document.body.style.backgroundPosition = 'center';
        document.body.style.backgroundAttachment = 'fixed';
    }
}

// 重置登录背景
function resetLoginBackground() {
    localStorage.removeItem('loginBackground');
    document.getElementById('loginBgPreview').style.display = 'none';
    document.querySelector('#loginBgPreview').parentNode.querySelector('.upload-placeholder').style.display = 'block';

    const loginModal = document.getElementById('loginModal');
    if (loginModal) {
        loginModal.style.backgroundImage = '';
    }

    utils.success('登录背景已重置');
}

// 重置主页背景
function resetMainBackground() {
    localStorage.removeItem('mainBackground');
    document.getElementById('mainBgPreview').style.display = 'none';
    document.querySelector('#mainBgPreview').parentNode.querySelector('.upload-placeholder').style.display = 'block';

    document.body.style.backgroundImage = '';

    utils.success('主页背景已重置');
}

// 页面加载时应用背景
document.addEventListener('DOMContentLoaded', function() {
    applyLoginBackground();
    applyMainBackground();
});
