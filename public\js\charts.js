// 图表管理类
class ChartManager {
    constructor() {
        this.charts = {};
        this.colors = {
            primary: ['#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF', '#FF9F40'],
            success: '#28a745',
            danger: '#dc3545',
            warning: '#ffc107',
            info: '#17a2b8'
        };
    }

    // 初始化所有图表
    initCharts(unitId = 'school') {
        this.destroyAllCharts();
        
        const students = this.getStudentsForUnit(unitId);
        
        this.createTotalChart(students);
        this.createReasonChart(students);
        this.createATypeChart(students);
        this.createAReasonChart(students);
        this.createBTypeChart(students);
        this.createBReasonChart(students);
    }

    // 销毁所有图表
    destroyAllCharts() {
        Object.values(this.charts).forEach(chart => {
            if (chart) {
                chart.destroy();
            }
        });
        this.charts = {};
    }

    // 根据单位获取学生数据
    getStudentsForUnit(unitId) {
        const allStudents = dataManager.getFilteredStudents();
        
        if (unitId === 'school') {
            return allStudents;
        }
        
        const unit = dataManager.getUnits().find(u => u.id === unitId);
        if (!unit) return allStudents;
        
        if (unit.type === 'college') {
            // 获取学院下所有班级的学生
            const classes = dataManager.getUnits().filter(u => u.parent === unitId);
            const classIds = classes.map(c => c.id);
            return allStudents.filter(s => classIds.includes(s.unit));
        } else if (unit.type === 'class') {
            // 获取班级的学生
            return allStudents.filter(s => s.unit === unitId);
        }
        
        return allStudents;
    }

    // 创建总体在外学生占比图表
    createTotalChart(students) {
        const ctx = document.getElementById('totalChart');
        if (!ctx) return;

        // 根据当前用户级别和位置类型计算实际状态
        const { presentCount, absentCount } = this.calculateEffectiveStatus(students);
        const total = students.length;

        this.charts.total = new Chart(ctx, {
            type: 'pie',
            data: {
                labels: ['在位', '不在位'],
                datasets: [{
                    data: [presentCount, absentCount],
                    backgroundColor: [this.colors.success, this.colors.danger],
                    borderWidth: 2,
                    borderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    },
                    tooltip: {
                        callbacks: {
                            label: (context) => {
                                const label = context.label;
                                const value = context.parsed;
                                const percentage = total > 0 ? ((value / total) * 100).toFixed(1) : 0;
                                return `${label}: ${value}人 (${percentage}%)`;
                            }
                        }
                    }
                }
            }
        });
    }

    // 创建各类外出人员占比图表
    createReasonChart(students) {
        const ctx = document.getElementById('reasonChart');
        if (!ctx) return;

        const absentStudents = students.filter(s => s.status === 'absent');
        const reasonCounts = {};
        
        absentStudents.forEach(s => {
            const reason = s.reason || '其他';
            reasonCounts[reason] = (reasonCounts[reason] || 0) + 1;
        });

        const labels = Object.keys(reasonCounts);
        const data = Object.values(reasonCounts);
        const total = absentStudents.length;

        this.charts.reason = new Chart(ctx, {
            type: 'pie',
            data: {
                labels: labels.map(label => this.getReasonText(label)),
                datasets: [{
                    data: data,
                    backgroundColor: this.colors.primary.slice(0, labels.length),
                    borderWidth: 2,
                    borderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    },
                    tooltip: {
                        callbacks: {
                            label: (context) => {
                                const label = context.label;
                                const value = context.parsed;
                                const percentage = total > 0 ? ((value / total) * 100).toFixed(1) : 0;
                                return `${label}: ${value}人 (${percentage}%)`;
                            }
                        }
                    }
                }
            }
        });
    }

    // 创建A类学生在外占比图表
    createATypeChart(students) {
        const ctx = document.getElementById('aTypeChart');
        if (!ctx) return;

        const aTypeStudents = students.filter(s => s.type === 'A类');
        const { presentCount, absentCount } = this.calculateEffectiveStatus(aTypeStudents);
        const total = aTypeStudents.length;

        this.charts.aType = new Chart(ctx, {
            type: 'pie',
            data: {
                labels: ['在位', '不在位'],
                datasets: [{
                    data: [presentCount, absentCount],
                    backgroundColor: [this.colors.success, this.colors.danger],
                    borderWidth: 2,
                    borderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    },
                    tooltip: {
                        callbacks: {
                            label: (context) => {
                                const label = context.label;
                                const value = context.parsed;
                                const percentage = total > 0 ? ((value / total) * 100).toFixed(1) : 0;
                                return `${label}: ${value}人 (${percentage}%)`;
                            }
                        }
                    }
                }
            }
        });
    }

    // 创建A类外出类型分布图表
    createAReasonChart(students) {
        const ctx = document.getElementById('aReasonChart');
        if (!ctx) return;

        const aTypeAbsentStudents = students.filter(s => s.type === 'A类' && s.status === 'absent');
        const reasonCounts = {};
        
        aTypeAbsentStudents.forEach(s => {
            const reason = s.reason || '其他';
            reasonCounts[reason] = (reasonCounts[reason] || 0) + 1;
        });

        const labels = Object.keys(reasonCounts);
        const data = Object.values(reasonCounts);
        const total = aTypeAbsentStudents.length;

        this.charts.aReason = new Chart(ctx, {
            type: 'pie',
            data: {
                labels: labels.map(label => this.getReasonText(label)),
                datasets: [{
                    data: data,
                    backgroundColor: this.colors.primary.slice(0, labels.length),
                    borderWidth: 2,
                    borderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    },
                    tooltip: {
                        callbacks: {
                            label: (context) => {
                                const label = context.label;
                                const value = context.parsed;
                                const percentage = total > 0 ? ((value / total) * 100).toFixed(1) : 0;
                                return `${label}: ${value}人 (${percentage}%)`;
                            }
                        }
                    }
                }
            }
        });
    }

    // 创建B类学生在外占比图表
    createBTypeChart(students) {
        const ctx = document.getElementById('bTypeChart');
        if (!ctx) return;

        const bTypeStudents = students.filter(s => s.type === 'B类');
        const { presentCount, absentCount } = this.calculateEffectiveStatus(bTypeStudents);
        const total = bTypeStudents.length;

        this.charts.bType = new Chart(ctx, {
            type: 'pie',
            data: {
                labels: ['在位', '不在位'],
                datasets: [{
                    data: [presentCount, absentCount],
                    backgroundColor: [this.colors.success, this.colors.danger],
                    borderWidth: 2,
                    borderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    },
                    tooltip: {
                        callbacks: {
                            label: (context) => {
                                const label = context.label;
                                const value = context.parsed;
                                const percentage = total > 0 ? ((value / total) * 100).toFixed(1) : 0;
                                return `${label}: ${value}人 (${percentage}%)`;
                            }
                        }
                    }
                }
            }
        });
    }

    // 创建B类外出类型分布图表
    createBReasonChart(students) {
        const ctx = document.getElementById('bReasonChart');
        if (!ctx) return;

        const bTypeAbsentStudents = students.filter(s => s.type === 'B类' && s.status === 'absent');
        const reasonCounts = {};
        
        bTypeAbsentStudents.forEach(s => {
            const reason = s.reason || '其他';
            reasonCounts[reason] = (reasonCounts[reason] || 0) + 1;
        });

        const labels = Object.keys(reasonCounts);
        const data = Object.values(reasonCounts);
        const total = bTypeAbsentStudents.length;

        this.charts.bReason = new Chart(ctx, {
            type: 'pie',
            data: {
                labels: labels.map(label => this.getReasonText(label)),
                datasets: [{
                    data: data,
                    backgroundColor: this.colors.primary.slice(0, labels.length),
                    borderWidth: 2,
                    borderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    },
                    tooltip: {
                        callbacks: {
                            label: (context) => {
                                const label = context.label;
                                const value = context.parsed;
                                const percentage = total > 0 ? ((value / total) * 100).toFixed(1) : 0;
                                return `${label}: ${value}人 (${percentage}%)`;
                            }
                        }
                    }
                }
            }
        });
    }

    // 计算有效状态（考虑单位内/单位外逻辑）
    calculateEffectiveStatus(students) {
        let presentCount = 0;
        let absentCount = 0;

        students.forEach(student => {
            if (student.status === 'present') {
                presentCount++;
            } else if (student.status === 'absent') {
                // 单位内不在位的特殊处理
                if (student.locationType === 'internal') {
                    // 对学校级别：单位内算在位
                    if (currentUser && currentUser.level === 'school') {
                        presentCount++;
                    } else {
                        // 对学院/班级级别：单位内算不在位
                        absentCount++;
                    }
                } else {
                    // 单位外统一算不在位
                    absentCount++;
                }
            }
        });

        return { presentCount, absentCount };
    }

    // 获取原因文本
    getReasonText(reason) {
        const reasonMap = {
            'vacation': '休假',
            'training': '培训',
            'secondment': '借调',
            'business': '出差',
            '': '其他'
        };
        return reasonMap[reason] || reason || '其他';
    }
}

// 创建图表管理实例
const chartManager = new ChartManager();

// 全局函数，供其他模块调用
function initCharts(unitId) {
    chartManager.initCharts(unitId);
}
