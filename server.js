const express = require('express');
const cors = require('cors');
const path = require('path');
const fs = require('fs');
const multer = require('multer');
const XLSX = require('xlsx');

const app = express();
const PORT = 3000;

// 中间件
app.use(cors());
app.use(express.json());
app.use(express.static('public'));

// 创建上传目录
const UPLOAD_DIR = path.join(__dirname, 'public', 'uploads');
if (!fs.existsSync(UPLOAD_DIR)) {
    fs.mkdirSync(UPLOAD_DIR, { recursive: true });
}

// 文件上传配置
const storage = multer.diskStorage({
    destination: function (req, file, cb) {
        if (file.fieldname === 'excel') {
            cb(null, UPLOAD_DIR); // Excel文件临时目录
        } else {
            cb(null, UPLOAD_DIR); // 图片文件保存到public/uploads
        }
    },
    filename: function (req, file, cb) {
        if (file.fieldname === 'excel') {
            cb(null, file.originalname); // Excel文件保持原名
        } else {
            // 图片文件生成唯一名称
            const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
            const ext = path.extname(file.originalname);
            cb(null, file.fieldname + '-' + uniqueSuffix + ext);
        }
    }
});

const upload = multer({
    storage: storage,
    limits: {
        fileSize: 5 * 1024 * 1024 // 5MB限制
    },
    fileFilter: function (req, file, cb) {
        if (file.fieldname === 'excel') {
            // Excel文件
            if (file.mimetype.includes('spreadsheet') || file.mimetype.includes('csv') || file.originalname.match(/\.(xlsx|xls|csv)$/)) {
                cb(null, true);
            } else {
                cb(new Error('只允许上传Excel文件'));
            }
        } else {
            // 图片文件
            if (file.mimetype.startsWith('image/')) {
                cb(null, true);
            } else {
                cb(new Error('只允许上传图片文件'));
            }
        }
    }
});

// 数据文件路径
const DATA_DIR = './data';
const UNITS_FILE = path.join(DATA_DIR, 'units.json');
const ACCOUNTS_FILE = path.join(DATA_DIR, 'accounts.json');
const STUDENTS_FILE = path.join(DATA_DIR, 'students.json');

// 确保数据目录存在
if (!fs.existsSync(DATA_DIR)) {
    fs.mkdirSync(DATA_DIR);
}

// 初始化数据文件
function initializeData() {
    // 初始化单位数据
    if (!fs.existsSync(UNITS_FILE)) {
        const units = [
            { id: 'school', name: '全校', type: 'school', parent: '' },
            { id: 'college1', name: '体育学院', type: 'college', parent: 'school' },
            { id: 'college2', name: '电子工程学院', type: 'college', parent: 'school' },
            { id: 'college3', name: '机械工程学院', type: 'college', parent: 'school' },
            { id: 'class101', name: '计算机1班', type: 'class', parent: 'college1' },
            { id: 'class102', name: '计算机2班', type: 'class', parent: 'college1' },
            { id: 'class103', name: '计算机3班', type: 'class', parent: 'college1' },
            { id: 'class201', name: '电子1班', type: 'class', parent: 'college2' },
            { id: 'class202', name: '电子2班', type: 'class', parent: 'college2' },
            { id: 'class301', name: '机械1班', type: 'class', parent: 'college3' },
            { id: 'class302', name: '机械2班', type: 'class', parent: 'college3' },
            { id: 'class303', name: '机械3班', type: 'class', parent: 'college3' }
        ];
        fs.writeFileSync(UNITS_FILE, JSON.stringify(units, null, 2));
    }

    // 初始化账号数据
    if (!fs.existsSync(ACCOUNTS_FILE)) {
        const accounts = [
            { username: 'admin_school', password: '123456', level: 'school', unit: 'school' },
            { username: 'admin_college1', password: '123456', level: 'college', unit: 'college1' },
            { username: 'admin_college2', password: '123456', level: 'college', unit: 'college2' },
            { username: 'admin_college3', password: '123456', level: 'college', unit: 'college3' },
            { username: 'admin_class101', password: '123456', level: 'class', unit: 'class101' },
            { username: 'admin_class102', password: '123456', level: 'class', unit: 'class102' }
        ];
        fs.writeFileSync(ACCOUNTS_FILE, JSON.stringify(accounts, null, 2));
    }

    // 初始化学生数据
    if (!fs.existsSync(STUDENTS_FILE)) {
        const students = [
            {
                name: "张三", id: "********", idNumber: "110101200001011234", 
                workDate: "2023-07-01", type: "A类", major: "计算机科学",
                status: "present", reason: "", location: "", locationType: "internal",
                contacts: {self: "***********", parent: "***********"},
                unit: "class101"
            },
            {
                name: "李四", id: "********", idNumber: "110101200002022345", 
                workDate: "2023-07-01", type: "B类", major: "软件工程",
                status: "absent", reason: "training", location: "北京", locationType: "external",
                contacts: {self: "***********", parent: "***********"},
                unit: "class102"
            },
            {
                name: "王五", id: "********", idNumber: "110101200003033456", 
                workDate: "2023-07-01", type: "A类", major: "电子工程",
                status: "absent", reason: "vacation", location: "上海", locationType: "external",
                contacts: {self: "13700137000", parent: "13700137001"},
                unit: "class201"
            }
        ];
        fs.writeFileSync(STUDENTS_FILE, JSON.stringify(students, null, 2));
    }
}

// 读取数据文件
function readDataFile(filePath) {
    try {
        return JSON.parse(fs.readFileSync(filePath, 'utf8'));
    } catch (error) {
        console.error(`读取文件失败: ${filePath}`, error);
        return [];
    }
}

// 写入数据文件
function writeDataFile(filePath, data) {
    try {
        fs.writeFileSync(filePath, JSON.stringify(data, null, 2));
        return true;
    } catch (error) {
        console.error(`写入文件失败: ${filePath}`, error);
        return false;
    }
}

// API路由

// 登录验证
app.post('/api/login', (req, res) => {
    const { username, password, level } = req.body;
    const accounts = readDataFile(ACCOUNTS_FILE);
    
    const account = accounts.find(acc => 
        acc.username === username && 
        acc.password === password &&
        acc.level === level
    );
    
    if (account) {
        res.json({ success: true, user: account });
    } else {
        res.json({ success: false, message: '用户名或密码错误，或账号类型不匹配' });
    }
});

// 获取单位数据
app.get('/api/units', (req, res) => {
    const units = readDataFile(UNITS_FILE);
    res.json(units);
});

// 获取账号数据
app.get('/api/accounts', (req, res) => {
    const accounts = readDataFile(ACCOUNTS_FILE);
    res.json(accounts);
});

// 获取学生数据
app.get('/api/students', (req, res) => {
    const students = readDataFile(STUDENTS_FILE);
    res.json(students);
});

// 保存学生数据
app.post('/api/students', (req, res) => {
    const students = req.body;
    if (writeDataFile(STUDENTS_FILE, students)) {
        res.json({ success: true });
    } else {
        res.status(500).json({ success: false, message: '保存失败' });
    }
});

// 保存单位数据
app.post('/api/units', (req, res) => {
    const units = req.body;
    if (writeDataFile(UNITS_FILE, units)) {
        res.json({ success: true });
    } else {
        res.status(500).json({ success: false, message: '保存失败' });
    }
});

// 保存账号数据
app.post('/api/accounts', (req, res) => {
    const accounts = req.body;
    if (writeDataFile(ACCOUNTS_FILE, accounts)) {
        res.json({ success: true });
    } else {
        res.status(500).json({ success: false, message: '保存失败' });
    }
});

// Excel文件上传和解析
app.post('/api/upload-excel', upload.single('excel'), (req, res) => {
    try {
        const workbook = XLSX.readFile(req.file.path);
        const sheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[sheetName];
        const data = XLSX.utils.sheet_to_json(worksheet, { raw: false, dateNF: 'yyyy-mm-dd' });

        // 删除临时文件
        fs.unlinkSync(req.file.path);

        res.json({ success: true, data });
    } catch (error) {
        console.error('Excel解析失败:', error);
        res.status(500).json({ success: false, message: 'Excel文件解析失败' });
    }
});

// 图片上传
app.post('/api/upload-image', upload.single('image'), (req, res) => {
    try {
        if (!req.file) {
            return res.status(400).json({ success: false, message: '没有上传文件' });
        }

        // 返回图片的访问URL
        const imageUrl = `/uploads/${req.file.filename}`;
        res.json({
            success: true,
            imageUrl: imageUrl,
            filename: req.file.filename
        });
    } catch (error) {
        console.error('图片上传失败:', error);
        res.status(500).json({ success: false, message: '图片上传失败' });
    }
});

// 获取服务器IP地址
app.get('/api/server-info', (req, res) => {
    const os = require('os');
    const interfaces = os.networkInterfaces();
    const networkIps = [];

    // 收集所有非内部IPv4地址
    for (const name of Object.keys(interfaces)) {
        for (const interface of interfaces[name]) {
            if (interface.family === 'IPv4' && !interface.internal) {
                networkIps.push(interface.address);
            }
        }
    }

    // 主IP地址（第一个非内部地址）
    const primaryIp = networkIps.length > 0 ? networkIps[0] : 'localhost';

    res.json({
        ip: primaryIp,
        port: PORT,
        networkIps: networkIps,
        allAddresses: networkIps.map(ip => `http://${ip}:${PORT}`)
    });
});

// 启动服务器
app.listen(PORT, '0.0.0.0', () => {
    initializeData();
    console.log(`\n=================================`);
    console.log(`在外学生统计管理系统已启动`);
    console.log(`=================================`);
    console.log(`本地访问: http://localhost:${PORT}`);
    console.log(`局域网访问: http://[本机IP]:${PORT}`);
    console.log(`数据存储目录: ${DATA_DIR}`);
    console.log(`=================================\n`);
    
    // 显示本机IP地址
    const os = require('os');
    const interfaces = os.networkInterfaces();
    console.log('可用的网络地址:');
    for (const name of Object.keys(interfaces)) {
        for (const interface of interfaces[name]) {
            if (interface.family === 'IPv4' && !interface.internal) {
                console.log(`  http://${interface.address}:${PORT}`);
            }
        }
    }
    console.log('\n');
});
