/* 基础样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', <PERSON><PERSON>, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    background: rgba(255, 255, 255, 0.95);
    min-height: 100vh;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
}

/* 头部样式 */
header {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    color: white;
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.art-title {
    font-size: 2.5em;
    font-weight: bold;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
    background: linear-gradient(45deg, #3498db, #e74c3c, #f39c12);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.header-info {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 10px;
}

.ip-address {
    font-size: 1.1em;
    display: flex;
    align-items: center;
    gap: 8px;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 15px;
}

/* 主要内容区域 */
.main-content {
    display: flex;
    min-height: calc(100vh - 100px);
}

/* 侧边栏样式 */
.sidebar {
    width: 320px;
    background: linear-gradient(180deg, #f8f9fa 0%, #e9ecef 100%);
    border-right: 1px solid #dee2e6;
    padding: 25px;
    overflow-y: auto;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
}

.account-info {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    padding: 20px;
    border-radius: 12px;
    margin-bottom: 25px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.8);
}

.account-info h3 {
    color: #2c3e50;
    margin-bottom: 15px;
    font-size: 1.1em;
    display: flex;
    align-items: center;
    gap: 8px;
}

.account-info h3::before {
    content: "👤";
    font-size: 1.2em;
}

.account-info p {
    margin: 8px 0;
    color: #495057;
}

.user-level {
    display: inline-block;
    padding: 6px 16px;
    border-radius: 25px;
    font-size: 0.85em;
    font-weight: bold;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
}

.user-level:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.user-level.school-level {
    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
    color: white;
}

.user-level.college-level {
    background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
    color: white;
}

.user-level.class-level {
    background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
    color: white;
}

/* 树形结构样式 */
.tree-container {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 25px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.8);
    overflow: hidden; /* 防止内容超出边界 */
}

.tree-title {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 18px;
    font-weight: bold;
    color: #2c3e50;
    font-size: 1.1em;
    padding-bottom: 10px;
    border-bottom: 2px solid #e9ecef;
}

.tree-title i {
    color: #3498db;
    font-size: 1.2em;
}

.unit-tree {
    list-style: none;
    padding: 0;
    margin: 0;
}

/* 树节点基础样式 */
.tree-node {
    margin: 4px 0;
    border-radius: 8px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.tree-node-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 12px;
    cursor: pointer;
    border: 1px solid transparent;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.tree-node:hover .tree-node-content {
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    border-color: #2196f3;
    box-shadow: 0 2px 8px rgba(33, 150, 243, 0.15);
}

.tree-node.selected .tree-node-content {
    background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%);
    color: white;
    box-shadow: 0 4px 12px rgba(33, 150, 243, 0.4);
}

.tree-node.selected .tree-action-btn {
    color: rgba(255, 255, 255, 0.8);
}

.tree-node.selected .tree-action-btn:hover {
    color: white;
    background: rgba(255, 255, 255, 0.2);
}

/* 树节点标签 */
.tree-node-label {
    display: flex;
    align-items: center;
    gap: 8px;
    flex: 1;
    font-weight: 500;
    user-select: none;
}

/* 树图标 */
.tree-icon {
    font-size: 1.1em;
    width: 20px;
    text-align: center;
    flex-shrink: 0;
}

.tree-expand-icon {
    font-size: 0.8em;
    width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 3px;
    transition: all 0.3s ease;
    cursor: pointer;
    margin-right: 4px;
    flex-shrink: 0;
}

.tree-expand-icon:hover {
    background: rgba(0, 0, 0, 0.1);
    transform: scale(1.2);
}

.tree-node.selected .tree-expand-icon:hover {
    background: rgba(255, 255, 255, 0.2);
}

.tree-expand-placeholder {
    width: 16px;
    height: 16px;
    margin-right: 4px;
    flex-shrink: 0;
}

.tree-text {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* 子节点容器 */
.tree-children {
    margin-left: 20px;
    padding-left: 16px;
    border-left: 2px solid #e9ecef;
    transition: all 0.3s ease;
    overflow: hidden;
}

.tree-children.expanded {
    max-height: 1000px;
    opacity: 1;
}

.tree-children.collapsed {
    max-height: 0;
    opacity: 0;
    margin-top: 0;
    margin-bottom: 0;
}

.tree-actions {
    display: flex;
    gap: 6px;
    opacity: 0;
    transition: all 0.3s ease;
    transform: translateX(10px);
}

.tree-node:hover .tree-actions {
    opacity: 1;
    transform: translateX(0);
}

.tree-action-btn {
    background: rgba(108, 117, 125, 0.1);
    border: none;
    padding: 6px 8px;
    border-radius: 6px;
    cursor: pointer;
    color: #6c757d;
    transition: all 0.3s ease;
    font-size: 0.85em;
    min-width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.tree-action-btn:hover {
    background: rgba(108, 117, 125, 0.2);
    color: #495057;
    transform: scale(1.1);
}

.tree-action-btn.delete-btn:hover {
    background: rgba(220, 53, 69, 0.1);
    color: #dc3545;
}

.tree-node.selected .tree-action-btn.delete-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    color: #ffcdd2;
}

/* 键盘导航支持 */
.unit-tree:focus {
    outline: none;
}

.tree-node:focus-within .tree-node-content {
    outline: 2px solid #3498db;
    outline-offset: 2px;
}

/* 管理员面板 */
.admin-panel {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.8);
}

.admin-panel h3 {
    color: #2c3e50;
    margin-bottom: 18px;
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 1.1em;
    padding-bottom: 10px;
    border-bottom: 2px solid #e9ecef;
}

.admin-panel h3 i {
    color: #e74c3c;
    font-size: 1.2em;
}

.admin-buttons {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

/* 内容区域 */
.content {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
}

/* 图表容器 */
.charts-container {
    margin-bottom: 30px;
}

.chart-row {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
}

.chart-item {
    flex: 1;
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    text-align: center;
}

.chart-item h3 {
    margin-bottom: 15px;
    color: #2c3e50;
    font-size: 1.1em;
}

.chart-item canvas {
    max-height: 200px;
}

/* 表格容器 */
.table-container {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.table-header {
    padding: 20px;
    border-bottom: 1px solid #dee2e6;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 15px;
}

.table-title {
    display: flex;
    align-items: center;
    gap: 10px;
}

.table-title h3 {
    color: #2c3e50;
    margin: 0;
}

.current-unit {
    color: #6c757d;
    font-size: 0.9em;
}

.table-actions {
    display: flex;
    gap: 15px;
    align-items: center;
    flex-wrap: wrap;
}

.filters {
    display: flex;
    gap: 10px;
}

.action-buttons {
    display: flex;
    gap: 10px;
}

/* 表格样式 */
.table-wrapper {
    overflow-x: auto;
}

.student-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.9em;
}

.student-table th,
.student-table td {
    padding: 12px 8px;
    text-align: left;
    border-bottom: 1px solid #dee2e6;
}

.student-table th {
    background: #f8f9fa;
    font-weight: bold;
    color: #495057;
    position: sticky;
    top: 0;
    z-index: 10;
}

.student-table tr:hover {
    background: #f8f9fa;
}

.status-present {
    color: #28a745;
    font-weight: bold;
}

.status-absent {
    color: #dc3545;
    font-weight: bold;
}

.status-internal {
    color: #ffc107;
    font-weight: bold;
}

.reason-vacation {
    background: #d4edda;
    color: #155724;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 0.8em;
}

.reason-training {
    background: #d1ecf1;
    color: #0c5460;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 0.8em;
}

.reason-secondment {
    background: #f8d7da;
    color: #721c24;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 0.8em;
}

.reason-business {
    background: #fff3cd;
    color: #856404;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 0.8em;
}

.table-footer {
    padding: 15px 20px;
    background: #f8f9fa;
    border-top: 1px solid #dee2e6;
}

.stats-info {
    display: flex;
    gap: 20px;
    font-size: 0.9em;
}

.stats-info span {
    color: #6c757d;
}

.stats-info strong {
    color: #2c3e50;
}

/* 按钮样式 */
.btn {
    padding: 12px 20px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 0.95em;
    font-weight: 600;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.btn-primary {
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
    color: white;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #2980b9 0%, #1f5f8b 100%);
}

.btn-secondary {
    background: linear-gradient(135deg, #6c757d 0%, #545b62 100%);
    color: white;
}

.btn-secondary:hover {
    background: linear-gradient(135deg, #545b62 0%, #3d4449 100%);
}

.btn-success {
    background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
    color: white;
}

.btn-success:hover {
    background: linear-gradient(135deg, #229954 0%, #1e7e34 100%);
}

.btn-warning {
    background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
    color: white;
}

.btn-warning:hover {
    background: linear-gradient(135deg, #e67e22 0%, #d35400 100%);
}

.btn-danger {
    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
    color: white;
}

.btn-danger:hover {
    background: linear-gradient(135deg, #c0392b 0%, #a93226 100%);
}

.btn-info {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
    color: white;
}

.btn-info:hover {
    background: linear-gradient(135deg, #138496 0%, #0f6674 100%);
}

.action-btn {
    padding: 4px 8px;
    margin: 0 2px;
    border: none;
    border-radius: 3px;
    cursor: pointer;
    font-size: 0.8em;
}

.edit-btn {
    background: #ffc107;
    color: #212529;
}

.edit-btn:hover {
    background: #e0a800;
}

.delete-btn {
    background: #dc3545;
    color: white;
}

.delete-btn:hover {
    background: #c82333;
}

/* 模态框样式 */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.4) 0%, rgba(0, 0, 0, 0.6) 100%);
    backdrop-filter: blur(5px);
}

.modal.active {
    display: flex;
    align-items: center;
    justify-content: center;
    animation: modalFadeIn 0.3s ease-out;
}

@keyframes modalFadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

.modal-content {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: 16px;
    padding: 0;
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.8);
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        transform: translateY(-50px) scale(0.9);
        opacity: 0;
    }
    to {
        transform: translateY(0) scale(1);
        opacity: 1;
    }
}

.modal-content.large {
    max-width: 850px;
}

.modal-header {
    padding: 25px 30px;
    border-bottom: 2px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
    color: white;
    border-radius: 16px 16px 0 0;
}

.modal-header h2 {
    margin: 0;
    color: white;
    font-size: 1.4em;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
}

.modal-header h2::before {
    content: "📝";
    font-size: 1.2em;
}

.close {
    font-size: 24px;
    font-weight: bold;
    cursor: pointer;
    color: rgba(255, 255, 255, 0.8);
    transition: all 0.3s ease;
    padding: 5px 10px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
}

.close:hover {
    color: white;
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.1);
}

/* 表单样式 */
.form-group {
    margin-bottom: 20px;
}

.form-row {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
}

.form-row .form-group {
    flex: 1;
    margin-bottom: 0;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #2c3e50;
    font-size: 0.95em;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 0.95em;
    transition: all 0.3s ease;
    background: #ffffff;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
    background: #f8f9fa;
    transform: translateY(-1px);
}

.form-group input:hover,
.form-group select:hover,
.form-group textarea:hover {
    border-color: #bdc3c7;
}

/* 表单验证样式 */
.form-group input.error,
.form-group select.error,
.form-group textarea.error {
    border-color: #e74c3c;
    background: #fdf2f2;
    box-shadow: 0 0 0 3px rgba(231, 76, 60, 0.1);
}

.field-error {
    color: #e74c3c;
    font-size: 0.85em;
    margin-top: 5px;
    display: flex;
    align-items: center;
    gap: 5px;
    animation: errorSlideIn 0.3s ease-out;
}

.field-error::before {
    content: "⚠️";
    font-size: 0.9em;
}

@keyframes errorSlideIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 成功状态 */
.form-group input.success,
.form-group select.success,
.form-group textarea.success {
    border-color: #27ae60;
    background: #f2f9f5;
}

/* 加载状态 */
.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

.btn:disabled::before {
    display: none;
}

/* 模态框内容区域 */
.modal-body {
    padding: 25px 30px;
}

.form-actions {
    padding: 25px 30px;
    border-top: 2px solid #e9ecef;
    display: flex;
    gap: 15px;
    justify-content: flex-end;
    background: #f8f9fa;
    border-radius: 0 0 16px 16px;
}

/* 登录相关样式 */
.login-help {
    margin-top: 25px;
    padding: 20px;
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    border-radius: 12px;
    border-left: 4px solid #2196f3;
    box-shadow: 0 4px 12px rgba(33, 150, 243, 0.1);
}

.login-help h4 {
    margin-bottom: 15px;
    color: #1976d2;
    font-size: 1.1em;
    display: flex;
    align-items: center;
    gap: 8px;
}

.login-help h4::before {
    content: "💡";
    font-size: 1.2em;
}

.login-help p {
    margin: 8px 0;
    font-size: 0.9em;
    color: #1565c0;
    padding: 4px 8px;
    background: rgba(255, 255, 255, 0.5);
    border-radius: 6px;
    font-family: 'Courier New', monospace;
}

/* 导入相关样式 */
.import-content {
    padding: 30px;
}

/* 导入说明样式 */
.import-instructions {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 1px solid #dee2e6;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 25px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.import-instructions p {
    margin: 0 0 15px 0;
    font-weight: bold;
    color: #495057;
    display: flex;
    align-items: center;
    gap: 8px;
}

.import-instructions p i {
    color: #17a2b8;
}

.import-instructions ul {
    margin: 15px 0;
    padding-left: 25px;
}

.import-instructions li {
    margin: 8px 0;
    color: #6c757d;
    line-height: 1.5;
}

.import-instructions .btn-link {
    background: none;
    border: none;
    color: #007bff;
    text-decoration: none;
    cursor: pointer;
    padding: 8px 12px;
    margin-top: 15px;
    border-radius: 6px;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 6px;
    font-weight: 500;
}

.import-instructions .btn-link:hover {
    color: #0056b3;
    background: rgba(0, 123, 255, 0.1);
    transform: translateY(-1px);
}

.upload-area {
    margin-bottom: 25px;
}

.upload-box {
    border: 3px dashed #bdc3c7;
    border-radius: 12px;
    padding: 50px 30px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    position: relative;
    overflow: hidden;
}

.upload-box::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(52, 152, 219, 0.1), transparent);
    transform: rotate(45deg);
    transition: all 0.5s ease;
    opacity: 0;
}

.upload-box:hover::before {
    opacity: 1;
    animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

.upload-box:hover {
    border-color: #3498db;
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(52, 152, 219, 0.2);
}

.upload-box i {
    font-size: 4em;
    color: #7f8c8d;
    margin-bottom: 20px;
    transition: all 0.3s ease;
}

.upload-box:hover i {
    color: #3498db;
    transform: scale(1.1);
}

.upload-box p {
    margin: 15px 0 8px;
    font-weight: bold;
    color: #2c3e50;
    font-size: 1.1em;
}

.upload-box small {
    color: #7f8c8d;
    font-size: 0.9em;
}

/* 通知系统样式 */
.notification-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
    pointer-events: none;
}

.notification {
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    margin-bottom: 10px;
    min-width: 300px;
    max-width: 400px;
    opacity: 0;
    transform: translateX(100%);
    transition: all 0.3s ease;
    pointer-events: auto;
    border-left: 4px solid #3498db;
}

.notification.show {
    opacity: 1;
    transform: translateX(0);
}

.notification.hide {
    opacity: 0;
    transform: translateX(100%);
}

.notification-success {
    border-left-color: #27ae60;
}

.notification-error {
    border-left-color: #e74c3c;
}

.notification-warning {
    border-left-color: #f39c12;
}

.notification-info {
    border-left-color: #3498db;
}

.notification-content {
    display: flex;
    align-items: flex-start;
    padding: 15px;
    gap: 12px;
}

.notification-icon {
    font-size: 1.2em;
    flex-shrink: 0;
    margin-top: 2px;
}

.notification-message {
    flex: 1;
    font-size: 0.95em;
    line-height: 1.4;
    color: #2c3e50;
}

.notification-close {
    background: none;
    border: none;
    font-size: 1.5em;
    color: #95a5a6;
    cursor: pointer;
    padding: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s ease;
    flex-shrink: 0;
}

.notification-close:hover {
    background: #ecf0f1;
    color: #7f8c8d;
}

/* 确认对话框样式 */
.confirm-dialog-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 10000;
    display: none;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(3px);
}

.confirm-dialog-overlay.active {
    display: flex;
    animation: fadeIn 0.3s ease-out;
}

.confirm-dialog {
    background: white;
    border-radius: 12px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    max-width: 400px;
    width: 90%;
    animation: slideIn 0.3s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-30px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.confirm-dialog-header {
    padding: 20px 25px 15px;
    border-bottom: 1px solid #e9ecef;
}

.confirm-dialog-header h3 {
    margin: 0;
    color: #2c3e50;
    font-size: 1.2em;
}

.confirm-dialog-body {
    padding: 20px 25px;
}

.confirm-message {
    margin: 0;
    color: #495057;
    line-height: 1.5;
    font-size: 1em;
}

.confirm-dialog-footer {
    padding: 15px 25px 25px;
    display: flex;
    gap: 12px;
    justify-content: flex-end;
}

/* 设置模态框样式 */
.settings-tabs {
    display: flex;
    border-bottom: 2px solid #e9ecef;
    margin-bottom: 25px;
}

.tab-btn {
    background: none;
    border: none;
    padding: 12px 20px;
    cursor: pointer;
    font-size: 0.95em;
    color: #6c757d;
    border-bottom: 3px solid transparent;
    transition: all 0.3s ease;
}

.tab-btn:hover {
    color: #495057;
    background: #f8f9fa;
}

.tab-btn.active {
    color: #3498db;
    border-bottom-color: #3498db;
    background: #f8f9fa;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

.tab-content h3 {
    margin-bottom: 20px;
    color: #2c3e50;
    border-bottom: 1px solid #e9ecef;
    padding-bottom: 10px;
}

/* 背景上传样式 */
.background-upload {
    display: flex;
    gap: 15px;
    align-items: flex-start;
}

.upload-preview {
    width: 200px;
    height: 120px;
    border: 2px dashed #ced4da;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.upload-preview:hover {
    border-color: #3498db;
    background: #f8f9fa;
}

.upload-preview img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.upload-placeholder {
    text-align: center;
    color: #6c757d;
    font-size: 0.9em;
}

/* 图表设置样式 */
.chart-settings {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
}

.chart-settings label {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 12px;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.chart-settings label:hover {
    background: #f8f9fa;
    border-color: #3498db;
}

.chart-settings input[type="checkbox"] {
    width: 18px;
    height: 18px;
}

/* 管理表格样式 */
.management-table {
    margin-top: 20px;
}

.settings-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 15px;
}

.settings-table th,
.settings-table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #e9ecef;
}

.settings-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #495057;
}

.settings-table tr:hover {
    background: #f8f9fa;
}

.settings-table .action-buttons {
    display: flex;
    gap: 8px;
}

.settings-table .action-btn {
    padding: 4px 8px;
    font-size: 0.8em;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .chart-row {
        flex-direction: column;
    }

    .chart-item {
        margin-bottom: 15px;
    }
}

@media (max-width: 768px) {
    .main-content {
        flex-direction: column;
    }

    .sidebar {
        width: 100%;
        order: 2;
    }

    .content {
        order: 1;
    }

    .art-title {
        font-size: 1.8em;
    }

    .header-info {
        align-items: flex-start;
    }

    .table-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .table-actions {
        width: 100%;
        justify-content: space-between;
    }

    .form-row {
        flex-direction: column;
    }
}
