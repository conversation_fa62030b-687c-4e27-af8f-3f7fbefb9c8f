// API接口封装
class API {
    constructor() {
        this.baseURL = '';
    }

    // 通用请求方法
    async request(url, options = {}) {
        try {
            const response = await fetch(this.baseURL + url, {
                headers: {
                    'Content-Type': 'application/json',
                    ...options.headers
                },
                ...options
            });
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            return await response.json();
        } catch (error) {
            console.error('API请求失败:', error);
            throw error;
        }
    }

    // 登录验证
    async login(username, password, level) {
        return await this.request('/api/login', {
            method: 'POST',
            body: JSON.stringify({ username, password, level })
        });
    }

    // 获取单位数据
    async getUnits() {
        return await this.request('/api/units');
    }

    // 获取账号数据
    async getAccounts() {
        return await this.request('/api/accounts');
    }

    // 获取学生数据
    async getStudents() {
        return await this.request('/api/students');
    }

    // 保存学生数据
    async saveStudents(students) {
        return await this.request('/api/students', {
            method: 'POST',
            body: JSON.stringify(students)
        });
    }

    // 保存单位数据
    async saveUnits(units) {
        return await this.request('/api/units', {
            method: 'POST',
            body: JSON.stringify(units)
        });
    }

    // 保存账号数据
    async saveAccounts(accounts) {
        return await this.request('/api/accounts', {
            method: 'POST',
            body: JSON.stringify(accounts)
        });
    }

    // 上传Excel文件
    async uploadExcel(file) {
        const formData = new FormData();
        formData.append('excel', file);
        
        try {
            const response = await fetch(this.baseURL + '/api/upload-excel', {
                method: 'POST',
                body: formData
            });
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            return await response.json();
        } catch (error) {
            console.error('Excel上传失败:', error);
            throw error;
        }
    }

    // 获取服务器信息
    async getServerInfo() {
        return await this.request('/api/server-info');
    }
}

// 创建API实例
const api = new API();

// 数据管理类
class DataManager {
    constructor() {
        this.units = [];
        this.accounts = [];
        this.students = [];
        this.currentUser = null;
    }

    // 初始化数据
    async init() {
        try {
            this.units = await api.getUnits();
            this.accounts = await api.getAccounts();
            this.students = await api.getStudents();
        } catch (error) {
            console.error('数据初始化失败:', error);
            alert('数据加载失败，请检查服务器连接');
        }
    }

    // 刷新数据
    async refresh() {
        await this.init();
        // 触发界面更新
        if (typeof renderStudentTable === 'function') {
            renderStudentTable();
        }
        if (typeof renderUnitTree === 'function') {
            renderUnitTree();
        }
        if (typeof updateStats === 'function') {
            updateStats();
        }
        if (typeof initCharts === 'function') {
            initCharts(currentUnit);
        }
    }

    // 获取单位数据
    getUnits() {
        return this.units;
    }

    // 获取账号数据
    getAccounts() {
        return this.accounts;
    }

    // 获取学生数据
    getStudents() {
        return this.students;
    }

    // 保存学生数据
    async saveStudents(students) {
        try {
            const result = await api.saveStudents(students);
            if (result.success) {
                this.students = students;
                return true;
            }
            return false;
        } catch (error) {
            console.error('保存学生数据失败:', error);
            return false;
        }
    }

    // 保存单位数据
    async saveUnits(units) {
        try {
            const result = await api.saveUnits(units);
            if (result.success) {
                this.units = units;
                return true;
            }
            return false;
        } catch (error) {
            console.error('保存单位数据失败:', error);
            return false;
        }
    }

    // 保存账号数据
    async saveAccounts(accounts) {
        try {
            const result = await api.saveAccounts(accounts);
            if (result.success) {
                this.accounts = accounts;
                return true;
            }
            return false;
        } catch (error) {
            console.error('保存账号数据失败:', error);
            return false;
        }
    }

    // 设置当前用户
    setCurrentUser(user) {
        this.currentUser = user;
    }

    // 获取当前用户
    getCurrentUser() {
        return this.currentUser;
    }

    // 根据权限过滤学生数据
    getFilteredStudents() {
        if (!this.currentUser) {
            return this.students;
        }

        if (this.currentUser.level === 'school') {
            // 学校管理员可以查看所有学生
            return this.students;
        } else if (this.currentUser.level === 'college') {
            // 学院管理员只能查看本学院学生
            const collegeClasses = this.units.filter(u => u.parent === this.currentUser.unit).map(u => u.id);
            return this.students.filter(s => collegeClasses.includes(s.unit));
        } else if (this.currentUser.level === 'class') {
            // 班级管理员只能查看本班级学生
            return this.students.filter(s => s.unit === this.currentUser.unit);
        }

        return [];
    }

    // 检查用户是否有权限操作某个单位
    hasPermission(unitId) {
        if (!this.currentUser) {
            return false;
        }

        if (this.currentUser.level === 'school') {
            return true;
        } else if (this.currentUser.level === 'college') {
            const unit = this.units.find(u => u.id === unitId);
            if (!unit) return false;
            
            // 可以操作本学院和下属班级
            return unit.id === this.currentUser.unit || unit.parent === this.currentUser.unit;
        } else if (this.currentUser.level === 'class') {
            // 只能操作本班级
            return unitId === this.currentUser.unit;
        }

        return false;
    }
}

// 创建数据管理实例
const dataManager = new DataManager();

// 消息提示管理类
class NotificationManager {
    constructor() {
        this.container = null;
        this.notifications = new Map();
        this.init();
    }

    // 初始化容器
    init() {
        this.container = document.createElement('div');
        this.container.className = 'notification-container';
        document.body.appendChild(this.container);
    }

    // 显示通知
    show(message, type = 'info', duration = 4000) {
        const id = this.generateId();
        const notification = this.createNotification(id, message, type);

        this.notifications.set(id, notification);
        this.container.appendChild(notification);

        // 触发动画
        setTimeout(() => {
            notification.classList.add('show');
        }, 10);

        // 自动关闭
        if (duration > 0) {
            setTimeout(() => {
                this.hide(id);
            }, duration);
        }

        return id;
    }

    // 创建通知元素
    createNotification(id, message, type) {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.dataset.id = id;

        const icon = this.getIcon(type);

        notification.innerHTML = `
            <div class="notification-content">
                <div class="notification-icon">${icon}</div>
                <div class="notification-message">${message}</div>
                <button class="notification-close" onclick="notificationManager.hide('${id}')">×</button>
            </div>
        `;

        return notification;
    }

    // 获取图标
    getIcon(type) {
        const icons = {
            success: '✅',
            error: '❌',
            warning: '⚠️',
            info: 'ℹ️'
        };
        return icons[type] || icons.info;
    }

    // 隐藏通知
    hide(id) {
        const notification = this.notifications.get(id);
        if (!notification) return;

        notification.classList.add('hide');

        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
            this.notifications.delete(id);
        }, 300);
    }

    // 清除所有通知
    clearAll() {
        this.notifications.forEach((notification, id) => {
            this.hide(id);
        });
    }

    // 生成唯一ID
    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }
}

// 确认对话框管理类
class ConfirmDialog {
    constructor() {
        this.dialog = null;
        // 延迟初始化，确保DOM已加载
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.init());
        } else {
            this.init();
        }
    }

    // 初始化对话框
    init() {
        if (this.dialog) return; // 避免重复初始化

        // 创建确认对话框
        this.dialog = document.createElement('div');
        this.dialog.className = 'confirm-dialog-overlay';

        // 创建对话框内容
        const dialogContent = document.createElement('div');
        dialogContent.className = 'confirm-dialog';

        // 创建头部
        const header = document.createElement('div');
        header.className = 'confirm-dialog-header';
        header.innerHTML = '<h3>确认操作</h3>';

        // 创建内容区
        const body = document.createElement('div');
        body.className = 'confirm-dialog-body';
        const message = document.createElement('p');
        message.className = 'confirm-message';
        body.appendChild(message);

        // 创建底部按钮
        const footer = document.createElement('div');
        footer.className = 'confirm-dialog-footer';

        const cancelBtn = document.createElement('button');
        cancelBtn.className = 'btn btn-secondary confirm-cancel';
        cancelBtn.textContent = '取消';

        const okBtn = document.createElement('button');
        okBtn.className = 'btn btn-danger confirm-ok';
        okBtn.textContent = '确认';

        footer.appendChild(cancelBtn);
        footer.appendChild(okBtn);

        // 组装对话框
        dialogContent.appendChild(header);
        dialogContent.appendChild(body);
        dialogContent.appendChild(footer);
        this.dialog.appendChild(dialogContent);
        document.body.appendChild(this.dialog);
        console.log('确认对话框已初始化');
    }

    // 显示确认对话框
    show(message, options = {}) {
        return new Promise((resolve) => {
            // 确保对话框已初始化
            if (!this.dialog) {
                console.log('对话框未初始化，正在初始化...');
                this.init();
            }

            // 再次检查对话框是否存在
            if (!this.dialog) {
                console.error('无法创建确认对话框');
                resolve(false);
                return;
            }

            // 等待DOM更新后再查找元素
            setTimeout(() => {
                const messageElement = this.dialog.querySelector('.confirm-message');
                const cancelBtn = this.dialog.querySelector('.confirm-cancel');
                const okBtn = this.dialog.querySelector('.confirm-ok');

                // 检查元素是否存在
                if (!messageElement || !cancelBtn || !okBtn) {
                    console.error('确认对话框元素未找到', {
                        dialog: this.dialog,
                        messageElement,
                        cancelBtn,
                        okBtn,
                        innerHTML: this.dialog.innerHTML
                    });
                    resolve(false);
                    return;
                }

                messageElement.textContent = message;

                // 设置按钮文本
                cancelBtn.textContent = options.cancelText || '取消';
                okBtn.textContent = options.confirmText || '确认';

                // 重置按钮样式，保持原有的类名
                if (okBtn) {
                    // 保持confirm-ok类名，只修改样式类
                    okBtn.className = `btn ${options.confirmClass || 'btn-danger'} confirm-ok`;
                }

                // 显示对话框
                this.dialog.classList.add('active');

                // 绑定事件
                const handleCancel = () => {
                    this.hide();
                    resolve(false);
                    cleanup();
                };

                const handleConfirm = () => {
                    this.hide();
                    resolve(true);
                    cleanup();
                };

                const handleKeydown = (e) => {
                    if (e.key === 'Escape') {
                        handleCancel();
                    } else if (e.key === 'Enter') {
                        handleConfirm();
                    }
                };

                const cleanup = () => {
                    if (cancelBtn) cancelBtn.removeEventListener('click', handleCancel);
                    if (okBtn) okBtn.removeEventListener('click', handleConfirm);
                    document.removeEventListener('keydown', handleKeydown);
                };

                if (cancelBtn) cancelBtn.addEventListener('click', handleCancel);
                if (okBtn) okBtn.addEventListener('click', handleConfirm);
                document.addEventListener('keydown', handleKeydown);

                // 聚焦到确认按钮
                if (okBtn) {
                    setTimeout(() => okBtn.focus(), 100);
                }
            }, 50); // 增加延时
        });
    }

    // 隐藏对话框
    hide() {
        this.dialog.classList.remove('active');
    }
}

// 创建实例
const notificationManager = new NotificationManager();
const confirmDialog = new ConfirmDialog();

// 工具函数
const utils = {
    // 格式化日期
    formatDate(date) {
        if (!date) return '';
        const d = new Date(date);
        return d.toLocaleDateString('zh-CN');
    },

    // 生成唯一ID
    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    },

    // 显示消息
    showMessage(message, type = 'info', duration = 4000) {
        return notificationManager.show(message, type, duration);
    },

    // 确认对话框
    async confirm(message, options = {}) {
        return await confirmDialog.show(message, options);
    },

    // 成功消息
    success(message) {
        return this.showMessage(message, 'success');
    },

    // 错误消息
    error(message) {
        return this.showMessage(message, 'error');
    },

    // 警告消息
    warning(message) {
        return this.showMessage(message, 'warning');
    },

    // 信息消息
    info(message) {
        return this.showMessage(message, 'info');
    }
};
