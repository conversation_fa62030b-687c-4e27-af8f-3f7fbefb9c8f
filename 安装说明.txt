在外学生统计管理系统 - 安装说明
=====================================

系统要求：
- Windows 10 或更高版本
- Node.js 14.0 或更高版本

安装步骤：

1. 安装Node.js
   - 访问 https://nodejs.org/
   - 下载并安装LTS版本
   - 安装完成后重启电脑

2. 运行系统
   - 双击 start.bat 文件
   - 首次运行会自动安装依赖包
   - 等待服务器启动完成

3. 访问系统
   - 打开浏览器
   - 访问 http://localhost:3000
   - 或访问显示的局域网地址

默认账号：
- 学校管理员：admin_school / 123456
- 学院管理员：admin_college1 / 123456  
- 班级管理员：admin_class101 / 123456

注意事项：
- 确保3000端口未被占用
- 如需局域网访问，请关闭防火墙或开放3000端口
- 数据保存在data目录下，请定期备份

故障排除：
- 如果无法启动，请检查Node.js是否正确安装
- 如果端口被占用，可修改server.js中的PORT变量
- 如果依赖安装失败，请检查网络连接

技术支持：
如有问题请联系系统管理员
