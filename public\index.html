<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>在外学生统计管理系统</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/fontawesome.min.css">
</head>
<body>
    <!-- 登录模态框 -->
    <div id="loginModal" class="modal active">
        <div class="modal-content">
            <div class="modal-header">
                <h2>系统登录</h2>
            </div>
            <form id="loginForm">
                <div class="modal-body">
                    <div class="form-group">
                        <label for="loginUsername">用户名:</label>
                        <input type="text" id="loginUsername" required>
                    </div>
                    <div class="form-group">
                        <label for="loginPassword">密码:</label>
                        <input type="password" id="loginPassword" required>
                    </div>
                    <div class="form-group">
                        <label for="loginType">账号类型:</label>
                        <select id="loginType" required>
                            <option value="school">学校级管理员</option>
                            <option value="college">学院级管理员</option>
                            <option value="class">班级管理员</option>
                        </select>
                    </div>
                    <div class="login-help">
                        <h4>默认账号信息：</h4>
                        <p>学校管理员: admin_school / 123456</p>
                        <p>学院管理员: admin_college1 / 123456</p>
                        <p>班级管理员: admin_class101 / 123456</p>
                    </div>
                </div>
                <div class="form-actions">
                    <button type="button" onclick="login()" class="btn btn-primary">登录</button>
                </div>
            </form>
        </div>
    </div>

    <div class="container">
        <header>
            <div class="art-title">在外学生统计管理系统</div>
            <div class="header-info">
                <div class="ip-address">
                    <i class="fas fa-network-wired"></i> 服务器: <span id="currentIp">加载中...</span>
                </div>
                <div class="user-info">
                    <span id="currentUser">未登录</span>
                    <button class="btn btn-secondary" onclick="logout()">退出登录</button>
                </div>
            </div>
        </header>
        
        <div class="main-content">
            <div class="sidebar">
                <div class="account-info">
                    <h3>当前账号</h3>
                    <p>管理员 <strong id="currentUserName">-</strong></p>
                    <span class="user-level" id="userLevel">-</span>
                </div>
                
                <div class="tree-container">
                    <div class="tree-title">
                        <i class="fas fa-sitemap"></i>
                        <span>单位管理树</span>
                    </div>
                    <ul class="unit-tree" id="unitTree">
                        <!-- 单位树将通过JS动态加载 -->
                    </ul>
                </div>
                
                <div class="admin-panel" id="adminPanel">
                    <h3><i class="fas fa-user-cog"></i> 管理员面板</h3>
                    <div class="admin-buttons">
                        <button class="btn btn-primary" onclick="openAccountModal()">
                            <i class="fas fa-user-plus"></i> 添加账号
                        </button>
                        <button class="btn btn-warning" onclick="openUnitModal()">
                            <i class="fas fa-plus-circle"></i> 添加单位
                        </button>
                        <button class="btn btn-info" onclick="refreshData()">
                            <i class="fas fa-sync-alt"></i> 刷新数据
                        </button>
                        <button class="btn btn-secondary" onclick="openSettingsModal()">
                            <i class="fas fa-cog"></i> 系统设置
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="content">
                <div class="charts-container">
                    <div class="chart-row">
                        <div class="chart-item">
                            <h3>总体在外学生占比</h3>
                            <canvas id="totalChart"></canvas>
                        </div>
                        <div class="chart-item">
                            <h3>各类外出人员占比</h3>
                            <canvas id="reasonChart"></canvas>
                        </div>
                    </div>
                    <div class="chart-row">
                        <div class="chart-item">
                            <h3>A类学生在外占比</h3>
                            <canvas id="aTypeChart"></canvas>
                        </div>
                        <div class="chart-item">
                            <h3>A类外出类型分布</h3>
                            <canvas id="aReasonChart"></canvas>
                        </div>
                    </div>
                    <div class="chart-row">
                        <div class="chart-item">
                            <h3>B类学生在外占比</h3>
                            <canvas id="bTypeChart"></canvas>
                        </div>
                        <div class="chart-item">
                            <h3>B类外出类型分布</h3>
                            <canvas id="bReasonChart"></canvas>
                        </div>
                    </div>
                </div>
                
                <div class="table-container">
                    <div class="table-header">
                        <div class="table-title">
                            <h3><i class="fas fa-users"></i> 学生信息管理</h3>
                            <span class="current-unit">当前单位: <span id="current-unit">全校</span></span>
                        </div>
                        <div class="table-actions">
                            <div class="filters">
                                <select id="statusFilter" onchange="renderStudentTable()">
                                    <option value="">全部状态</option>
                                    <option value="present">在位</option>
                                    <option value="absent">不在位</option>
                                </select>
                                <select id="typeFilter" onchange="renderStudentTable()">
                                    <option value="">全部类型</option>
                                    <option value="A类">A类</option>
                                    <option value="B类">B类</option>
                                </select>
                            </div>
                            <div class="action-buttons">
                                <button class="btn btn-success" onclick="openAddModal()">
                                    <i class="fas fa-plus"></i> 添加学生
                                </button>
                                <button class="btn btn-info" onclick="openImportModal()">
                                    <i class="fas fa-file-excel"></i> 批量导入
                                </button>
                                <button class="btn btn-warning" onclick="exportExcel()">
                                    <i class="fas fa-file-download"></i> 批量导出
                                </button>
                                <button class="btn btn-secondary" onclick="downloadTemplate()">
                                    <i class="fas fa-file-alt"></i> 下载模板
                                </button>
                                <button class="btn btn-danger" onclick="batchDelete()">
                                    <i class="fas fa-trash"></i> 批量删除
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="table-wrapper">
                        <table class="student-table">
                            <thead>
                                <tr>
                                    <th><input type="checkbox" id="selectAll" onchange="toggleSelectAll()"> 序号</th>
                                    <th>姓名</th>
                                    <th>学号/工号</th>
                                    <th>身份证号</th>
                                    <th>参加工作时间</th>
                                    <th>人员类别</th>
                                    <th>专业</th>
                                    <th>单位</th>
                                    <th>在位状态</th>
                                    <th>外出原因</th>
                                    <th>外出地点</th>
                                    <th>联系方式</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="student-table-body">
                                <!-- 学生数据将通过JS动态加载 -->
                            </tbody>
                        </table>
                    </div>
                    
                    <div class="table-footer">
                        <div class="stats-info">
                            <span>总人数: <strong id="totalCount">0</strong></span>
                            <span>在位: <strong id="presentCount">0</strong></span>
                            <span>不在位: <strong id="absentCount">0</strong></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 添加/编辑学生模态框 -->
    <div id="addModal" class="modal">
        <div class="modal-content large">
            <div class="modal-header">
                <h2 id="modalTitle">添加学生</h2>
                <span class="close" onclick="closeModal('addModal')">&times;</span>
            </div>
            <form id="studentForm">
                <div class="modal-body">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="studentName">姓名 *</label>
                            <input type="text" id="studentName" required>
                        </div>
                    <div class="form-group">
                        <label for="studentId">学号/工号 *</label>
                        <input type="text" id="studentId" required>
                    </div>
                    <div class="form-group">
                        <label for="studentIdNumber">身份证号 *</label>
                        <input type="text" id="studentIdNumber" required>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="studentWorkDate">参加工作时间 *</label>
                        <input type="date" id="studentWorkDate" required>
                    </div>
                    <div class="form-group">
                        <label for="studentType">人员类别 *</label>
                        <select id="studentType" required>
                            <option value="A类">A类</option>
                            <option value="B类">B类</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="studentMajor">专业 *</label>
                        <input type="text" id="studentMajor" required>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="studentUnit">单位 *</label>
                        <select id="studentUnit" required>
                            <!-- 单位选项将通过JS动态加载 -->
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="studentStatus">在位状态 *</label>
                        <select id="studentStatus" required onchange="toggleAbsentFields()">
                            <option value="present">在位</option>
                            <option value="absent">不在位</option>
                        </select>
                    </div>
                </div>
                <div id="absentFields" style="display: none;">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="studentReason">外出原因</label>
                            <select id="studentReason">
                                <option value="">请选择</option>
                                <option value="vacation">休假</option>
                                <option value="training">培训</option>
                                <option value="secondment">借调</option>
                                <option value="business">出差</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="studentLocation">外出地点</label>
                            <input type="text" id="studentLocation">
                        </div>
                        <div class="form-group">
                            <label for="studentLocationType">位置类型</label>
                            <select id="studentLocationType">
                                <option value="internal">单位内</option>
                                <option value="external">单位外</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="studentSelfContact">本人联系方式</label>
                        <input type="text" id="studentSelfContact">
                    </div>
                    <div class="form-group">
                        <label for="studentParentContact">父母联系方式</label>
                        <input type="text" id="studentParentContact">
                    </div>
                </div>
                </div>
                <div class="form-actions">
                    <button type="button" onclick="saveStudent()" class="btn btn-primary">保存</button>
                    <button type="button" onclick="closeModal('addModal')" class="btn btn-secondary">取消</button>
                </div>
            </form>
        </div>
    </div>

    <!-- 导入Excel模态框 -->
    <div id="importModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>从CSV导入学生</h2>
                <span class="close" onclick="closeModal('importModal')">&times;</span>
            </div>
            <div class="modal-body">
                <div class="import-instructions">
                    <p><i class="fas fa-info-circle"></i> 导入说明：</p>
                    <ul>
                        <li>请使用标准模板格式的CSV文件</li>
                        <li>必填字段：姓名、学号/工号</li>
                        <li>支持 .csv 格式</li>
                    </ul>
                    <button class="btn btn-link" onclick="downloadTemplate()">
                        <i class="fas fa-download"></i> 下载导入模板
                    </button>
                </div>
                <div class="upload-area">
                    <input type="file" id="excelFile" accept=".csv" style="display: none;">
                    <div class="upload-box" onclick="document.getElementById('excelFile').click()">
                        <i class="fas fa-cloud-upload-alt"></i>
                        <p>点击选择CSV文件</p>
                        <small>支持 .csv 格式</small>
                    </div>
                </div>
            </div>
            <div class="form-actions">
                    <button type="button" onclick="importExcel()" class="btn btn-primary">开始导入</button>
                    <button type="button" onclick="closeModal('importModal')" class="btn btn-secondary">取消</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 添加单位模态框 -->
    <div id="addUnitModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="unitModalTitle">添加单位</h2>
                <span class="close" onclick="closeModal('addUnitModal')">&times;</span>
            </div>
            <form id="unitForm">
                <div class="modal-body">
                    <div class="form-group">
                        <label for="unitName">单位名称 *</label>
                        <input type="text" id="unitName" required placeholder="请输入单位名称">
                    </div>
                    <div class="form-group">
                        <label for="unitType">单位类型 *</label>
                        <select id="unitType" required onchange="toggleParentUnit()">
                            <option value="">请选择单位类型</option>
                            <option value="college">学院</option>
                            <option value="class">班级</option>
                        </select>
                    </div>
                    <div class="form-group" id="parentUnitGroup" style="display: none;">
                        <label for="parentUnit">上级单位 *</label>
                        <select id="parentUnit">
                            <option value="">请选择上级单位</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="unitDescription">单位描述</label>
                        <textarea id="unitDescription" rows="3" placeholder="请输入单位描述（可选）"></textarea>
                    </div>
                </div>
                <div class="form-actions">
                    <button type="button" onclick="saveUnit()" class="btn btn-primary">保存</button>
                    <button type="button" onclick="closeModal('addUnitModal')" class="btn btn-secondary">取消</button>
                </div>
            </form>
        </div>
    </div>

    <!-- 添加账号模态框 -->
    <div id="addAccountModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>添加管理账号</h2>
                <span class="close" onclick="closeModal('addAccountModal')">&times;</span>
            </div>
            <form id="accountForm">
                <div class="modal-body">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="accountUsername">用户名 *</label>
                            <input type="text" id="accountUsername" required placeholder="请输入用户名">
                        </div>
                        <div class="form-group">
                            <label for="accountPassword">密码 *</label>
                            <input type="password" id="accountPassword" required placeholder="请输入密码">
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="accountLevel">账号级别 *</label>
                            <select id="accountLevel" required onchange="loadAccountUnits()">
                                <option value="">请选择账号级别</option>
                                <option value="school">学校级管理员</option>
                                <option value="college">学院级管理员</option>
                                <option value="class">班级管理员</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="accountUnit">管理单位 *</label>
                            <select id="accountUnit" required>
                                <option value="">请先选择账号级别</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="accountDescription">账号描述</label>
                        <textarea id="accountDescription" rows="2" placeholder="请输入账号描述（可选）"></textarea>
                    </div>
                </div>
                <div class="form-actions">
                    <button type="button" onclick="saveAccount()" class="btn btn-primary">保存</button>
                    <button type="button" onclick="closeModal('addAccountModal')" class="btn btn-secondary">取消</button>
                </div>
            </form>
        </div>
    </div>

    <!-- 系统设置模态框 -->
    <div id="settingsModal" class="modal">
        <div class="modal-content large">
            <div class="modal-header">
                <h2>系统设置</h2>
                <span class="close" onclick="closeModal('settingsModal')">&times;</span>
            </div>
            <div class="modal-body">
                <div class="settings-tabs">
                    <button class="tab-btn active" onclick="switchTab('background')">背景设置</button>
                    <button class="tab-btn" onclick="switchTab('charts')">图表设置</button>
                    <button class="tab-btn" onclick="switchTab('units')">单位管理</button>
                    <button class="tab-btn" onclick="switchTab('accounts')">账号管理</button>
                </div>

                <!-- 背景设置 -->
                <div id="backgroundTab" class="tab-content active">
                    <h3>背景图片设置</h3>
                    <div class="form-group">
                        <label>登录页面背景</label>
                        <div class="background-upload">
                            <input type="file" id="loginBgFile" accept="image/*" style="display: none;">
                            <div class="upload-preview" onclick="document.getElementById('loginBgFile').click()">
                                <img id="loginBgPreview" src="" alt="点击上传背景图片" style="display: none;">
                                <div class="upload-placeholder">点击上传登录背景图片</div>
                            </div>
                            <button class="btn btn-secondary" onclick="resetLoginBackground()">恢复默认</button>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>主页面背景</label>
                        <div class="background-upload">
                            <input type="file" id="mainBgFile" accept="image/*" style="display: none;">
                            <div class="upload-preview" onclick="document.getElementById('mainBgFile').click()">
                                <img id="mainBgPreview" src="" alt="点击上传背景图片" style="display: none;">
                                <div class="upload-placeholder">点击上传主页背景图片</div>
                            </div>
                            <button class="btn btn-secondary" onclick="resetMainBackground()">恢复默认</button>
                        </div>
                    </div>
                </div>

                <!-- 图表设置 -->
                <div id="chartsTab" class="tab-content">
                    <h3>图表显示设置</h3>
                    <div class="chart-settings">
                        <label><input type="checkbox" id="showTotalChart" checked> 总体在外学生占比</label>
                        <label><input type="checkbox" id="showReasonChart" checked> 各类外出人员占比</label>
                        <label><input type="checkbox" id="showATypeChart" checked> A类学生在外占比</label>
                        <label><input type="checkbox" id="showAReasonChart" checked> A类外出类型分布</label>
                        <label><input type="checkbox" id="showBTypeChart" checked> B类学生在外占比</label>
                        <label><input type="checkbox" id="showBReasonChart" checked> B类外出类型分布</label>
                    </div>
                </div>

                <!-- 单位管理 -->
                <div id="unitsTab" class="tab-content">
                    <h3>单位管理</h3>
                    <div class="management-table">
                        <div class="table-header">
                            <button class="btn btn-primary" onclick="openUnitModal()">添加单位</button>
                        </div>
                        <table class="settings-table">
                            <thead>
                                <tr>
                                    <th>单位名称</th>
                                    <th>单位类型</th>
                                    <th>上级单位</th>
                                    <th>创建时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="unitsTableBody">
                                <!-- 动态加载 -->
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- 账号管理 -->
                <div id="accountsTab" class="tab-content">
                    <h3>账号管理</h3>
                    <div class="management-table">
                        <div class="table-header">
                            <button class="btn btn-primary" onclick="openAccountModal()">添加账号</button>
                            <button class="btn btn-warning" onclick="openChangePasswordModal()">修改密码</button>
                        </div>
                        <table class="settings-table">
                            <thead>
                                <tr>
                                    <th>用户名</th>
                                    <th>账号级别</th>
                                    <th>管理单位</th>
                                    <th>创建时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="accountsTableBody">
                                <!-- 动态加载 -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="form-actions">
                <button type="button" onclick="saveSettings()" class="btn btn-primary">保存设置</button>
                <button type="button" onclick="closeModal('settingsModal')" class="btn btn-secondary">关闭</button>
            </div>
        </div>
    </div>

    <!-- 修改密码模态框 -->
    <div id="changePasswordModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>修改密码</h2>
                <span class="close" onclick="closeModal('changePasswordModal')">&times;</span>
            </div>
            <form id="changePasswordForm">
                <div class="modal-body">
                    <div class="form-group">
                        <label for="changePasswordUsername">选择账号 *</label>
                        <select id="changePasswordUsername" required>
                            <option value="">请选择要修改密码的账号</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="newPassword">新密码 *</label>
                        <input type="password" id="newPassword" required placeholder="请输入新密码">
                    </div>
                    <div class="form-group">
                        <label for="confirmPassword">确认密码 *</label>
                        <input type="password" id="confirmPassword" required placeholder="请再次输入新密码">
                    </div>
                </div>
                <div class="form-actions">
                    <button type="button" onclick="changePassword()" class="btn btn-primary">修改密码</button>
                    <button type="button" onclick="closeModal('changePasswordModal')" class="btn btn-secondary">取消</button>
                </div>
            </form>
        </div>
    </div>

    <script src="js/chart.min.js"></script>
    <script src="js/xlsx.full.min.js"></script>
    <script src="js/api.js"></script>
    <script src="js/charts.js"></script>
    <script src="js/app.js"></script>
</body>
</html>
