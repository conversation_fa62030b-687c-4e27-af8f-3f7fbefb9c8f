// 简化的Chart.js实现 - 仅支持饼图
class Chart {
    constructor(ctx, config) {
        this.ctx = ctx.getContext('2d');
        this.canvas = ctx;
        this.config = config;
        this.data = config.data;
        this.options = config.options || {};
        
        this.render();
    }
    
    render() {
        const canvas = this.canvas;
        const ctx = this.ctx;
        
        // 清空画布
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        
        if (this.config.type === 'pie') {
            this.renderPieChart();
        }
    }
    
    renderPieChart() {
        const ctx = this.ctx;
        const canvas = this.canvas;
        const data = this.data.datasets[0].data;
        const labels = this.data.labels;
        const colors = this.data.datasets[0].backgroundColor;
        
        // 设置画布大小
        const size = Math.min(canvas.width, canvas.height) * 0.8;
        const centerX = canvas.width / 2;
        const centerY = canvas.height / 2;
        const radius = size / 2;
        
        // 计算总数
        const total = data.reduce((sum, value) => sum + value, 0);
        
        if (total === 0) {
            // 显示无数据
            ctx.fillStyle = '#666';
            ctx.font = '16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('暂无数据', centerX, centerY);
            return;
        }
        
        // 绘制饼图
        let currentAngle = -Math.PI / 2; // 从顶部开始
        
        data.forEach((value, index) => {
            const sliceAngle = (value / total) * 2 * Math.PI;
            
            // 绘制扇形
            ctx.beginPath();
            ctx.moveTo(centerX, centerY);
            ctx.arc(centerX, centerY, radius, currentAngle, currentAngle + sliceAngle);
            ctx.closePath();
            ctx.fillStyle = colors[index] || this.getDefaultColor(index);
            ctx.fill();
            
            // 绘制边框
            ctx.strokeStyle = '#fff';
            ctx.lineWidth = 2;
            ctx.stroke();
            
            // 绘制标签
            const labelAngle = currentAngle + sliceAngle / 2;
            const labelX = centerX + Math.cos(labelAngle) * (radius * 0.7);
            const labelY = centerY + Math.sin(labelAngle) * (radius * 0.7);
            
            const percentage = ((value / total) * 100).toFixed(1);
            
            ctx.fillStyle = '#fff';
            ctx.font = 'bold 12px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(`${percentage}%`, labelX, labelY);
            ctx.fillText(`${value}`, labelX, labelY + 15);
            
            currentAngle += sliceAngle;
        });
        
        // 绘制图例
        this.renderLegend();
    }
    
    renderLegend() {
        const ctx = this.ctx;
        const canvas = this.canvas;
        const labels = this.data.labels;
        const colors = this.data.datasets[0].backgroundColor;
        
        const legendY = canvas.height - 60;
        const legendItemWidth = canvas.width / labels.length;
        
        labels.forEach((label, index) => {
            const x = legendItemWidth * index + legendItemWidth / 2;
            
            // 绘制颜色块
            ctx.fillStyle = colors[index] || this.getDefaultColor(index);
            ctx.fillRect(x - 8, legendY, 16, 16);
            
            // 绘制标签
            ctx.fillStyle = '#333';
            ctx.font = '12px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(label, x, legendY + 30);
        });
    }
    
    getDefaultColor(index) {
        const defaultColors = [
            '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', 
            '#9966FF', '#FF9F40', '#FF6384', '#C9CBCF'
        ];
        return defaultColors[index % defaultColors.length];
    }
    
    destroy() {
        // 清空画布
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
    }
    
    update() {
        this.render();
    }
}

// 兼容性处理
if (typeof window !== 'undefined') {
    window.Chart = Chart;
}
