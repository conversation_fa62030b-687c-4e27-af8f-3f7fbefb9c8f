/*! xlsx.js (C) 2013-present SheetJS -- http://sheetjs.com */
var DO_NOT_EXPORT_CODEPAGE=true;var cptable={version:"1.15.0"};cptable[437]=function(){var e="\0\b\t\n\x0B\f\r !\"#$%&'()*+,-./0123456789:;<=>?@ABCDEFGHIJKLMNOPQRSTUVWXYZ[\\]^_`abcdefghijklmnopqrstuvwxyz{|}~ÇüéâäàåçêëèïîìÄÅÉæÆôöòûùÿÖÜ¢£¥₧ƒáíóúñÑªº¿⌐¬½¼¡«»░▒▓│┤╡╢╖╕╣║╗╝╜╛┐└┴┬├─┼╞╟╚╔╩╦╠═╬╧╨╤╥╙╘╒╓╫╪┘┌█▄▌▐▀αßΓπΣσµτΦΘΩδ∞φε∩≡±≥≤⌠⌡÷≈°∙·√ⁿ²■ ",r=[],t={};for(var a=0;a!=e.length;++a){if(e.charCodeAt(a)!==65533)t[e.charAt(a)]=a;r[a]=e.charAt(a)}return{enc:t,dec:r}}();cptable[620]=function(){var e="\0\b\t\n\x0B\f\r !\"#$%&'()*+,-./0123456789:;<=>?@ABCDEFGHIJKLMNOPQRSTUVWXYZ[\\]^_`abcdefghijklmnopqrstuvwxyz{|}~ÇüéâäàąçêëèïîćÄĄĘęłôöĆûùŚÖÜ¢Ł¥śƒŹŻóÓńŃźż¿⌐¬½¼¡«»░▒▓│┤╡╢╖╕╣║╗╝╜╛┐└┴┬├─┼╞╟╚╔╩╦╠═╬╧╨╤╥╙╘╒╓╫╪┘┌█▄▌▐▀αßΓπΣσµτΦΘΩδ∞φε∩≡±≥≤⌠⌡÷≈°∙·√ⁿ²■ ",r=[],t={};for(var a=0;a!=e.length;++a){if(e.charCodeAt(a)!==65533)t[e.charAt(a)]=a;r[a]=e.charAt(a)}return{enc:t,dec:r}}();

// 简化版的XLSX库，只包含基本的导出功能
var XLSX = {
    utils: {
        json_to_sheet: function(data) {
            var ws = {};
            var range = {s: {c: 0, r: 0}, e: {c: 0, r: 0}};

            if (data.length === 0) return ws;

            var headers = Object.keys(data[0]);
            range.e.c = headers.length - 1;
            range.e.r = data.length;

            // 添加表头
            headers.forEach(function(header, col) {
                var cellRef = XLSX.utils.encode_cell({c: col, r: 0});
                ws[cellRef] = {v: header, t: 's'};
            });

            // 添加数据
            data.forEach(function(row, rowIndex) {
                headers.forEach(function(header, col) {
                    var cellRef = XLSX.utils.encode_cell({c: col, r: rowIndex + 1});
                    var value = row[header];
                    if (value !== null && value !== undefined) {
                        ws[cellRef] = {v: value, t: typeof value === 'number' ? 'n' : 's'};
                    }
                });
            });

            ws['!ref'] = XLSX.utils.encode_range(range);
            return ws;
        },

        book_new: function() {
            return {SheetNames: [], Sheets: {}};
        },

        book_append_sheet: function(wb, ws, name) {
            wb.SheetNames.push(name);
            wb.Sheets[name] = ws;
        },

        encode_cell: function(cell) {
            return String.fromCharCode(65 + cell.c) + (cell.r + 1);
        },

        encode_range: function(range) {
            return this.encode_cell(range.s) + ':' + this.encode_cell(range.e);
        }
    },

    writeFile: function(wb, filename) {
        // 创建CSV内容
        var csv = '';
        var sheetName = wb.SheetNames[0];
        var ws = wb.Sheets[sheetName];

        if (!ws['!ref']) return;

        var range = this.utils.decode_range(ws['!ref']);

        for (var R = range.s.r; R <= range.e.r; ++R) {
            var row = [];
            for (var C = range.s.c; C <= range.e.c; ++C) {
                var cellRef = this.utils.encode_cell({c: C, r: R});
                var cell = ws[cellRef];
                var value = cell ? cell.v : '';
                if (typeof value === 'string' && (value.includes(',') || value.includes('"'))) {
                    value = '"' + value.replace(/"/g, '""') + '"';
                }
                row.push(value);
            }
            csv += row.join(',') + '\n';
        }

        // 创建下载链接
        var blob = new Blob(['\ufeff' + csv], {type: 'text/csv;charset=utf-8;'});
        var link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = filename;
        link.style.display = 'none';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }
};

// 添加decode_range方法
XLSX.utils.decode_range = function(range) {
    var parts = range.split(':');
    return {
        s: this.decode_cell(parts[0]),
        e: this.decode_cell(parts[1])
    };
};

XLSX.utils.decode_cell = function(cell) {
    var match = cell.match(/([A-Z]+)(\d+)/);
    var col = 0;
    var colStr = match[1];
    for (var i = 0; i < colStr.length; i++) {
        col = col * 26 + (colStr.charCodeAt(i) - 64);
    }
    return {c: col - 1, r: parseInt(match[2]) - 1};
};
