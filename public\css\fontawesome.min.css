/* FontAwesome 简化版本 - 仅包含项目所需图标 */
.fas, .fa-solid {
    font-family: "Font Awesome 5 Free";
    font-weight: 900;
}

.far, .fa-regular {
    font-family: "Font Awesome 5 Free";
    font-weight: 400;
}

.fab, .fa-brands {
    font-family: "Font Awesome 5 Brands";
    font-weight: 400;
}

.fa, .fas, .far, .fab {
    display: inline-block;
    font-style: normal;
    font-variant: normal;
    text-rendering: auto;
    line-height: 1;
}

/* 图标内容 - 使用Unicode字符替代 */
.fa-school::before { content: "🏫"; }
.fa-network-wired::before { content: "🌐"; }
.fa-sitemap::before { content: "📊"; }
.fa-user-cog::before { content: "⚙️"; }
.fa-user-plus::before { content: "👤+"; }
.fa-plus-circle::before { content: "➕"; }
.fa-sync-alt::before { content: "🔄"; }
.fa-users::before { content: "👥"; }
.fa-plus::before { content: "+"; }
.fa-file-excel::before { content: "📊"; }
.fa-trash::before { content: "🗑️"; }
.fa-edit::before { content: "✏️"; }
.fa-building::before { content: "🏢"; }
.fa-folder::before { content: "📁"; }
.fa-folder-open::before { content: "📂"; }
.fa-cloud-upload-alt::before { content: "☁️"; }
